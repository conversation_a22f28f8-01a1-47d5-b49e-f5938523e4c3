I00000000 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con next skip pkn=586488139
I00000000 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt tx pkn=586487894 dcid=0xe42f745cd18b29f117c0d2d3e167bbab7623 scid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 version=0x00000001 type=Initial len=0
I00000000 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 Initial CRYPTO(0x06) offset=0 len=268
I00000000 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 Initial PADDING(0x00) len=861
I00000000 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=4055389157886155 timeout=999
Sent packet: local=[********]:35652 remote=[**************]:443 ecn=0x2 1200 bytes
Timer has already expired: 0.000312s
Set timer=0.998574s
Received packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 71 bytes
I00000002 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con recv packet len=71
I00000002 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt rx pkn=0 dcid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 scid=****************************************** version=0x00000001 type=Initial len=25
I00000002 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 0 Initial ACK(0x02) largest_ack=586487894 ack_delay=0(0) ack_range_count=0
I00000002 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 0 Initial ACK(0x02) range=[586487894..586487894] len=0
I00000002 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc latest_rtt=2 min_rtt=2 smoothed_rtt=2 rttvar=1 ack_delay=0
I00000002 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con path is not ECN capable
I00000002 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cca 1200 bytes acked, slow start cwnd=13200
I00000002 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=4055388167722419 timeout=6
I00000002 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt read packet 71 left 0
Set timer=0.006559s
Set timer=0.006548s
I00000009 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss detection timer fired
I00000009 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc pto_count=1
I00000009 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=4055388181502606 timeout=13
I00000009 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt tx pkn=586487895 dcid=****************************************** scid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 version=0x00000001 type=Initial len=0
I00000009 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487895 Initial PING(0x01)
I00000009 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487895 Initial PADDING(0x00) len=1133
I00000009 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=4055388181529652 timeout=13
Sent packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 1200 bytes
Set timer=0.000010s
Set timer=0.013172s
Received packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 1200 bytes
I00000010 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con recv packet len=1200
I00000010 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt rx pkn=1 dcid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 scid=****************************************** version=0x00000001 type=Initial len=111
I00000010 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 Initial CRYPTO(0x06) offset=0 len=90
I00000010 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con the negotiated version is 0x00000001
Ordered CRYPTO data in Initial crypto level
00000000  02 00 00 56 03 03 a5 03  f8 7b f2 86 89 5f ce e8  |...V.....{..._..|
00000010  85 89 35 ca 79 e0 6b 3d  fe b5 26 e7 87 f8 f4 cf  |..5.y.k=..&.....|
00000020  34 eb ed a2 91 a2 00 13  01 00 00 2e 00 2b 00 02  |4............+..|
00000030  03 04 00 33 00 24 00 1d  00 20 fd ae 63 c6 a1 2c  |...3.$... ..c..,|
00000040  eb bb 0e 37 79 95 fe a4  bb a3 a2 da 32 28 6a 07  |...7y.......2(j.|
00000050  3b 46 fa a1 7e 2f 38 e7  6f 74                    |;F..~/8.ot|
0000005a
I00000010 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt read packet 158 left 1042
I00000010 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt rx pkn=0 dcid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 scid=****************************************** version=0x00000001 type=Handshake len=996
I00000010 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 0 Handshake CRYPTO(0x06) offset=0 len=135
Ordered CRYPTO data in Handshake crypto level
00000000  08 00 00 83 00 81 00 00  00 00 00 10 00 05 00 03  |................|
00000010  02 68 33 00 39 00 70 04  04 88 30 00 00 09 01 03  |.h3.9.p...0.....|
00000020  08 02 40 80 05 04 80 10  00 00 06 04 80 10 00 00  |..@.............|
00000030  07 04 80 10 00 00 01 04  80 00 75 30 03 04 80 00  |..........u0....|
00000040  ff f7 0e 01 02 0b 01 19  0a 01 03 00 12 e4 2f 74  |............../t|
00000050  5c d1 8b 29 f1 17 c0 d2  d3 e1 67 bb ab 76 23 0f  |\..)......g..v#.|
00000060  14 54 aa 86 da e4 93 00  00 00 05 ae 8a b5 f5 75  |.T.............u|
00000070  76 5b 0a d7 41 02 10 96  e2 e5 80 c5 63 e3 e0 45  |v[..A.......c..E|
00000080  08 66 77 4c f1 8c bf                              |.fwL...|
00000087
I00000010 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 0 Handshake CRYPTO(0x06) offset=135 len=835
Ordered CRYPTO data in Handshake crypto level
00000000  0b 00 0a b9 00 00 0a b5  00 05 fd 30 82 05 f9 30  |...........0...0|
00000010  82 04 e1 a0 03 02 01 02  02 10 01 aa 0e 0b 16 74  |...............t|
00000020  eb 0d 04 a3 8d 54 f3 35  6a b3 30 0d 06 09 2a 86  |.....T.5j.0...*.|
00000030  48 86 f7 0d 01 01 0b 05  00 30 6e 31 0b 30 09 06  |H........0n1.0..|
00000040  03 55 04 06 13 02 55 53  31 15 30 13 06 03 55 04  |.U....US1.0...U.|
00000050  0a 13 0c 44 69 67 69 43  65 72 74 20 49 6e 63 31  |...DigiCert Inc1|
00000060  19 30 17 06 03 55 04 0b  13 10 77 77 77 2e 64 69  |.0...U....www.di|
00000070  67 69 63 65 72 74 2e 63  6f 6d 31 2d 30 2b 06 03  |gicert.com1-0+..|
00000080  55 04 03 13 24 45 6e 63  72 79 70 74 69 6f 6e 20  |U...$Encryption |
00000090  45 76 65 72 79 77 68 65  72 65 20 44 56 20 54 4c  |Everywhere DV TL|
000000a0  53 20 43 41 20 2d 20 47  32 30 1e 17 0d 32 34 30  |S CA - G20...240|
000000b0  39 30 33 30 30 30 30 30  30 5a 17 0d 32 34 31 32  |903000000Z..2412|
000000c0  30 31 32 33 35 39 35 39  5a 30 1b 31 19 30 17 06  |01235959Z0.1.0..|
000000d0  03 55 04 03 13 10 61 6c  69 79 75 6e 2e 68 61 77  |.U....aliyun.haw|
000000e0  6b 73 2e 74 6f 70 30 82  01 22 30 0d 06 09 2a 86  |ks.top0.."0...*.|
000000f0  48 86 f7 0d 01 01 01 05  00 03 82 01 0f 00 30 82  |H.............0.|
00000100  01 0a 02 82 01 01 00 d7  eb 57 2d 2b dd 5c 3e 97  |.........W-+.\>.|
00000110  d3 ee 01 55 8a 1e 7b b1  07 5d db d7 b1 27 d6 4f  |...U..{..]...'.O|
00000120  3f 23 ec 22 69 d7 2e 5e  96 67 ce 41 fe 45 b6 9a  |?#."i..^.g.A.E..|
00000130  ea 5e 35 18 fb 35 87 10  fa d9 8c 53 95 83 85 51  |.^5..5.....S...Q|
00000140  28 f5 7f 82 81 14 2e 9e  01 2e 6c 48 53 0e b3 65  |(.........lHS..e|
00000150  b6 c7 9c 48 70 25 6c 70  8c 49 da 09 7c f4 b3 90  |...Hp%lp.I..|...|
00000160  43 91 db 32 41 ca 89 c7  ed 34 d9 99 9e 8e b3 c2  |C..2A....4......|
00000170  e7 b0 54 d7 a4 df f7 8e  c5 23 3d da 6a e9 95 28  |..T......#=.j..(|
00000180  cf 54 18 75 46 ad 9e ad  29 ea f8 3c d7 15 77 1c  |.T.uF...)..<..w.|
00000190  15 a6 b8 36 71 a7 02 8d  12 b0 8f 97 5b d3 77 b3  |...6q.......[.w.|
000001a0  cd a1 95 4c 0d a4 1d aa  3f 50 44 0e 58 07 a0 5a  |...L....?PD.X..Z|
000001b0  04 9d 59 69 31 10 2f 00  4c 34 e5 63 01 2d 2e 61  |..Yi1./.L4.c.-.a|
000001c0  74 f1 7a 83 67 05 e0 91  fb d7 48 e1 d9 4d b9 f9  |t.z.g.....H..M..|
000001d0  82 69 0c 16 c2 39 48 e1  b0 1d e1 ac fd c7 a2 9f  |.i...9H.........|
000001e0  78 5d d3 b4 8c 2c 37 c1  78 04 b9 50 ee e1 59 14  |x]...,7.x..P..Y.|
000001f0  7c bd 2b c5 50 bc 4f eb  d2 d7 3d 9c 8d 6e 5d 0a  ||.+.P.O...=..n].|
00000200  fe b1 a9 59 f6 1f 2b 02  03 01 00 01 a3 82 02 e4  |...Y..+.........|
00000210  30 82 02 e0 30 1f 06 03  55 1d 23 04 18 30 16 80  |0...0...U.#..0..|
00000220  14 78 df 91 90 5f ee de  ac f6 c5 75 eb d5 4c 55  |.x..._.....u..LU|
00000230  53 ef 24 4a b6 30 1d 06  03 55 1d 0e 04 16 04 14  |S.$J.0...U......|
00000240  a2 bb dd d3 97 8c 52 9f  88 a7 44 dc 80 cf 47 18  |......R...D...G.|
00000250  48 3c 31 08 30 1b 06 03  55 1d 11 04 14 30 12 82  |H<1.0...U....0..|
00000260  10 61 6c 69 79 75 6e 2e  68 61 77 6b 73 2e 74 6f  |.aliyun.hawks.to|
00000270  70 30 3e 06 03 55 1d 20  04 37 30 35 30 33 06 06  |p0>..U. .70503..|
00000280  67 81 0c 01 02 01 30 29  30 27 06 08 2b 06 01 05  |g.....0)0'..+...|
00000290  05 07 02 01 16 1b 68 74  74 70 3a 2f 2f 77 77 77  |......http://www|
000002a0  2e 64 69 67 69 63 65 72  74 2e 63 6f 6d 2f 43 50  |.digicert.com/CP|
000002b0  53 30 0e 06 03 55 1d 0f  01 01 ff 04 04 03 02 05  |S0...U..........|
000002c0  a0 30 1d 06 03 55 1d 25  04 16 30 14 06 08 2b 06  |.0...U.%..0...+.|
000002d0  01 05 05 07 03 01 06 08  2b 06 01 05 05 07 03 02  |........+.......|
000002e0  30 81 80 06 08 2b 06 01  05 05 07 01 01 04 74 30  |0....+........t0|
000002f0  72 30 24 06 08 2b 06 01  05 05 07 30 01 86 18 68  |r0$..+.....0...h|
00000300  74 74 70 3a 2f 2f 6f 63  73 70 2e 64 69 67 69 63  |ttp://ocsp.digic|
00000310  65 72 74 2e 63 6f 6d 30  4a 06 08 2b 06 01 05 05  |ert.com0J..+....|
00000320  07 30 02 86 3e 68 74 74  70 3a 2f 2f 63 61 63 65  |.0..>http://cace|
00000330  72 74 73 2e 64 69 67 69  63 65 72 74 2e 63 6f 6d  |rts.digicert.com|
00000340  2f 45 6e                                          |/En|
00000343
I00000010 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt read packet 1042 left 0
I00000010 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con processing buffered handshake packet
Received packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 1200 bytes
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con recv packet len=1200
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt rx pkn=1 dcid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 scid=****************************************** version=0x00000001 type=Handshake len=1154
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 Handshake CRYPTO(0x06) offset=970 len=1132
Ordered CRYPTO data in Handshake crypto level
00000000  63 72 79 70 74 69 6f 6e  45 76 65 72 79 77 68 65  |cryptionEverywhe|
00000010  72 65 44 56 54 4c 53 43  41 2d 47 32 2e 63 72 74  |reDVTLSCA-G2.crt|
00000020  30 0c 06 03 55 1d 13 01  01 ff 04 02 30 00 30 82  |0...U.......0.0.|
00000030  01 7f 06 0a 2b 06 01 04  01 d6 79 02 04 02 04 82  |....+.....y.....|
00000040  01 6f 04 82 01 6b 01 69  00 76 00 ee cd d0 64 d5  |.o...k.i.v....d.|
00000050  db 1a ce c5 5c b7 9d b4  cd 13 a2 32 87 46 7c bc  |....\......2.F|.|
00000060  ec de c3 51 48 59 46 71  1f b5 9b 00 00 01 91 b7  |...QHYFq........|
00000070  b8 71 bd 00 00 04 03 00  47 30 45 02 21 00 e5 8a  |.q......G0E.!...|
00000080  68 47 43 96 f9 c2 e4 d3  39 b2 01 ae ba 7c f9 44  |hGC.....9....|.D|
00000090  86 42 3a 52 5b cc b2 15  7d dc 9c 25 9b 9a 02 20  |.B:R[...}..%... |
000000a0  45 2f 79 74 be 60 52 14  e9 07 e6 2d 05 68 3c 6b  |E/yt.`R....-.h<k|
000000b0  8a 12 33 8b 60 b5 cf 91  a1 fa 86 68 a5 11 bd 8c  |..3.`......h....|
000000c0  00 76 00 da b6 bf 6b 3f  b5 b6 22 9f 9b c2 bb 5c  |.v....k?.."....\|
000000d0  6b e8 70 91 71 6c bb 51  84 85 34 bd a4 3d 30 48  |k.p.ql.Q..4..=0H|
000000e0  d7 fb ab 00 00 01 91 b7  b8 71 be 00 00 04 03 00  |.........q......|
000000f0  47 30 45 02 21 00 c8 b9  60 8d 60 b5 9e 37 7a 58  |G0E.!...`.`..7zX|
00000100  0a f4 64 29 89 47 eb cc  ae 83 75 2d 34 e4 09 8d  |..d).G....u-4...|
00000110  cd 53 a2 21 a3 e6 02 20  16 c2 a0 19 64 93 ff 9c  |.S.!... ....d...|
00000120  26 ee d1 2a c0 f9 f5 57  4a be c6 72 64 20 2a 26  |&..*...WJ..rd *&|
00000130  65 8c 29 82 2a 7f 5e 0b  00 77 00 3f 17 4b 4f d7  |e.).*.^..w.?.KO.|
00000140  22 47 58 94 1d 65 1c 84  be 0d 12 ed 90 37 7f 1f  |"GX..e.......7..|
00000150  85 6a eb c1 bf 28 85 ec  f8 64 6e 00 00 01 91 b7  |.j...(...dn.....|
00000160  b8 72 6a 00 00 04 03 00  48 30 46 02 21 00 b9 ca  |.rj.....H0F.!...|
00000170  1e 25 27 69 a0 86 a7 2d  c1 68 14 61 67 99 76 69  |.%'i...-.h.ag.vi|
00000180  89 7a 77 85 ba 38 5f b8  b9 60 15 ad 25 d3 02 21  |.zw..8_..`..%..!|
00000190  00 b4 62 76 ed 33 4c 2a  bf 71 31 77 a3 22 c2 ab  |..bv.3L*.q1w."..|
000001a0  0b 80 79 a6 1b 55 f7 1e  82 f3 35 5c a7 bc d9 51  |..y..U....5\...Q|
000001b0  d1 30 0d 06 09 2a 86 48  86 f7 0d 01 01 0b 05 00  |.0...*.H........|
000001c0  03 82 01 01 00 dc b7 b1  a3 08 f1 6a dd 88 f2 d9  |...........j....|
000001d0  88 4a d6 2e a2 48 d4 a9  08 f9 07 b3 52 6a 35 81  |.J...H......Rj5.|
000001e0  a0 86 73 b7 f1 b9 c5 59  0b 6a 57 3f ed bd 51 fe  |..s....Y.jW?..Q.|
000001f0  eb 80 c6 c0 00 42 5f 20  91 26 96 5b 82 7b 83 2b  |.....B_ .&.[.{.+|
00000200  9c fb bd 39 6a 3d c6 be  9a 5a b8 b1 d3 fc ed 62  |...9j=...Z.....b|
00000210  ee 03 7e 9e 1f 71 d5 ac  00 1e de f6 20 c5 59 17  |..~..q...... .Y.|
00000220  a3 c8 ad b3 af fb 55 82  50 9c 37 a4 15 e5 7c f6  |......U.P.7...|.|
00000230  83 6c 02 f8 e1 0a a0 35  9b b8 f6 ca f1 c6 0a e9  |.l.....5........|
00000240  35 a1 c8 31 2a 51 db cc  a6 33 75 a6 c9 c3 cc f1  |5..1*Q...3u.....|
00000250  a5 cf 77 2c 07 34 82 9f  3d d0 07 e6 b3 fd c9 8d  |..w,.4..=.......|
00000260  ef b4 c8 a0 c5 2b 86 fc  f5 0a f7 68 ee b8 83 74  |.....+.....h...t|
00000270  3b 1b 90 8c 50 7b a7 48  20 5d fd ef cc be 38 96  |;...P{.H ]....8.|
00000280  0e 28 0a 41 be 6a 39 aa  0a e2 dc c5 c9 d0 9e 9f  |.(.A.j9.........|
00000290  6e 0a 43 61 56 ba c0 a0  3c 62 18 93 07 90 8d 85  |n.CaV...<b......|
000002a0  7d 91 fa da d8 08 6c e9  b6 62 de 6b 6c ba c2 61  |}.....l..b.kl..a|
000002b0  5a 31 2d e6 83 53 26 cd  60 4e da e7 c9 0b 8f 9b  |Z1-..S&.`N......|
000002c0  95 17 90 e3 37 00 00 00  04 ae 30 82 04 aa 30 82  |....7.....0...0.|
000002d0  03 92 a0 03 02 01 02 02  10 0d e0 ff b5 ee 62 cb  |..............b.|
000002e0  61 10 9f 60 8c 9c ed 5e  d3 30 0d 06 09 2a 86 48  |a..`...^.0...*.H|
000002f0  86 f7 0d 01 01 0b 05 00  30 61 31 0b 30 09 06 03  |........0a1.0...|
00000300  55 04 06 13 02 55 53 31  15 30 13 06 03 55 04 0a  |U....US1.0...U..|
00000310  13 0c 44 69 67 69 43 65  72 74 20 49 6e 63 31 19  |..DigiCert Inc1.|
00000320  30 17 06 03 55 04 0b 13  10 77 77 77 2e 64 69 67  |0...U....www.dig|
00000330  69 63 65 72 74 2e 63 6f  6d 31 20 30 1e 06 03 55  |icert.com1 0...U|
00000340  04 03 13 17 44 69 67 69  43 65 72 74 20 47 6c 6f  |....DigiCert Glo|
00000350  62 61 6c 20 52 6f 6f 74  20 47 32 30 1e 17 0d 31  |bal Root G20...1|
00000360  37 31 31 32 37 31 32 34  36 34 30 5a 17 0d 32 37  |71127124640Z..27|
00000370  31 31 32 37 31 32 34 36  34 30 5a 30 6e 31 0b 30  |1127124640Z0n1.0|
00000380  09 06 03 55 04 06 13 02  55 53 31 15 30 13 06 03  |...U....US1.0...|
00000390  55 04 0a 13 0c 44 69 67  69 43 65 72 74 20 49 6e  |U....DigiCert In|
000003a0  63 31 19 30 17 06 03 55  04 0b 13 10 77 77 77 2e  |c1.0...U....www.|
000003b0  64 69 67 69 63 65 72 74  2e 63 6f 6d 31 2d 30 2b  |digicert.com1-0+|
000003c0  06 03 55 04 03 13 24 45  6e 63 72 79 70 74 69 6f  |..U...$Encryptio|
000003d0  6e 20 45 76 65 72 79 77  68 65 72 65 20 44 56 20  |n Everywhere DV |
000003e0  54 4c 53 20 43 41 20 2d  20 47 32 30 82 01 22 30  |TLS CA - G20.."0|
000003f0  0d 06 09 2a 86 48 86 f7  0d 01 01 01 05 00 03 82  |...*.H..........|
00000400  01 0f 00 30 82 01 0a 02  82 01 01 00 ef 14 7f 8e  |...0............|
00000410  a2 fe 7a fb a6 48 13 0e  a9 c4 79 22 1f 08 5a af  |..z..H....y"..Z.|
00000420  3e 75 2a dd a1 75 b4 c2  79 86 1f 4c 9c ee 8b 9a  |>u*..u..y..L....|
00000430  de 54 74 77 c1 1b 00 bd  4a 2f 97 8c ad 76 72 36  |.Ttw....J/...vr6|
00000440  60 c4 e6 ec 2f a4 60 d6  78 ef 36 10 0c 27 82 6e  |`.../.`.x.6..'.n|
00000450  9c dd 09 18 64 49 19 27  af 6c 9b 00 de c7 3a f2  |....dI.'.l....:.|
00000460  76 cf 43 3b 8a a7 92 5c  f2 fa 6b ca              |v.C;...\..k.|
0000046c
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt read packet 1200 left 0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con processing buffered handshake packet
Received packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 1159 bytes
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con recv packet len=1159
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt rx pkn=2 dcid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 scid=****************************************** version=0x00000001 type=Handshake len=1113
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 2 Handshake CRYPTO(0x06) offset=2102 len=782
Ordered CRYPTO data in Handshake crypto level
00000000  9d a6 b6 cd fc a5 20 97  a2 b3 d1 fa c7 21 42 2b  |...... ......!B+|
00000010  0a 03 b3 92 43 53 23 70  53 74 77 bb 5b ad c7 96  |....CS#pStw.[...|
00000020  14 d6 f3 80 bd 9c b0 95  50 7a 88 0e 04 64 9e fc  |........Pz...d..|
00000030  a6 44 21 9c 3a 81 72 ca  78 57 bb 9a ea 67 35 82  |.D!.:.r.xW...g5.|
00000040  51 3a 2d a2 0b 5d 7e 1e  e1 7b f6 20 2d b4 c7 37  |Q:-..]~..{. -..7|
00000050  d8 2b fa 50 ec 62 c5 8f  f7 65 5f 8b ce 92 e7 92  |.+.P.b...e_.....|
00000060  51 6a f7 c5 ce 46 0c 24  20 92 f5 1e eb cf 85 af  |Qj...F.$ .......|
00000070  32 bd bf 96 e8 98 ac 95  92 4b f8 72 c5 b6 27 68  |2........K.r..'h|
00000080  c6 62 3b 42 6d d9 c8 85  7a e9 6e 77 dc 3b 06 16  |.b;Bm...z.nw.;..|
00000090  29 85 26 4c f7 cb 41 9e  1d 6b 92 54 c6 c8 95 fb  |).&L..A..k.T....|
000000a0  02 03 01 00 01 a3 82 01  4f 30 82 01 4b 30 1d 06  |........O0..K0..|
000000b0  03 55 1d 0e 04 16 04 14  78 df 91 90 5f ee de ac  |.U......x..._...|
000000c0  f6 c5 75 eb d5 4c 55 53  ef 24 4a b6 30 1f 06 03  |..u..LUS.$J.0...|
000000d0  55 1d 23 04 18 30 16 80  14 4e 22 54 20 18 95 e6  |U.#..0...N"T ...|
000000e0  e3 6e e6 0f fa fa b9 12  ed 06 17 8f 39 30 0e 06  |.n..........90..|
000000f0  03 55 1d 0f 01 01 ff 04  04 03 02 01 86 30 1d 06  |.U...........0..|
00000100  03 55 1d 25 04 16 30 14  06 08 2b 06 01 05 05 07  |.U.%..0...+.....|
00000110  03 01 06 08 2b 06 01 05  05 07 03 02 30 12 06 03  |....+.......0...|
00000120  55 1d 13 01 01 ff 04 08  30 06 01 01 ff 02 01 00  |U.......0.......|
00000130  30 34 06 08 2b 06 01 05  05 07 01 01 04 28 30 26  |04..+........(0&|
00000140  30 24 06 08 2b 06 01 05  05 07 30 01 86 18 68 74  |0$..+.....0...ht|
00000150  74 70 3a 2f 2f 6f 63 73  70 2e 64 69 67 69 63 65  |tp://ocsp.digice|
00000160  72 74 2e 63 6f 6d 30 42  06 03 55 1d 1f 04 3b 30  |rt.com0B..U...;0|
00000170  39 30 37 a0 35 a0 33 86  31 68 74 74 70 3a 2f 2f  |907.5.3.1http://|
00000180  63 72 6c 33 2e 64 69 67  69 63 65 72 74 2e 63 6f  |crl3.digicert.co|
00000190  6d 2f 44 69 67 69 43 65  72 74 47 6c 6f 62 61 6c  |m/DigiCertGlobal|
000001a0  52 6f 6f 74 47 32 2e 63  72 6c 30 4c 06 03 55 1d  |RootG2.crl0L..U.|
000001b0  20 04 45 30 43 30 37 06  09 60 86 48 01 86 fd 6c  | .E0C07..`.H...l|
000001c0  01 02 30 2a 30 28 06 08  2b 06 01 05 05 07 02 01  |..0*0(..+.......|
000001d0  16 1c 68 74 74 70 73 3a  2f 2f 77 77 77 2e 64 69  |..https://www.di|
000001e0  67 69 63 65 72 74 2e 63  6f 6d 2f 43 50 53 30 08  |gicert.com/CPS0.|
000001f0  06 06 67 81 0c 01 02 01  30 0d 06 09 2a 86 48 86  |..g.....0...*.H.|
00000200  f7 0d 01 01 0b 05 00 03  82 01 01 00 a0 1b 35 78  |..............5x|
00000210  22 ca 6a 42 ed 55 13 c5  46 30 48 27 d2 c9 2f 4b  |".jB.U..F0H'../K|
00000220  3c 65 6b a6 f1 4f 3f d5  33 15 91 03 01 9f e0 8f  |<ek..O?.3.......|
00000230  6f 9c 67 08 3d 06 48 0e  8b fd 5d 19 16 b3 0d a8  |o.g.=.H...].....|
00000240  18 ab ae be 41 db 8d 4e  7a c1 02 bf 12 22 f7 c6  |....A..Nz...."..|
00000250  d9 f7 48 7d fa af d7 e5  e3 32 38 3d f9 7f c6 13  |..H}.....28=....|
00000260  68 43 29 76 74 02 9d ac  08 c2 21 f1 d3 af f4 05  |hC)vt.....!.....|
00000270  f8 93 8f e6 ff 5d 08 a1  fa 6a ad dd 95 de c4 d0  |.....]...j......|
00000280  89 a8 a7 b1 76 20 76 21  61 92 29 c8 1a 84 f6 9c  |....v v!a.).....|
00000290  5a 49 3f 04 04 05 8a c2  88 15 81 2a 1f 35 7d ef  |ZI?........*.5}.|
000002a0  84 b7 14 0c ff f0 b2 1f  0f 9f 31 9e c1 0d 19 fc  |..........1.....|
000002b0  f2 20 39 07 dc 9a f0 61  9f 1c 68 f4 df e9 95 fd  |. 9....a..h.....|
000002c0  33 51 ab 21 23 7c 1e 75  93 b5 d3 42 1f 6c 44 6a  |3Q.!#|.u...B.lDj|
000002d0  34 5a fe 1f 59 05 78 d4  c8 52 90 ac 69 36 9f 68  |4Z..Y.x..R..i6.h|
000002e0  5c 2e b7 d9 49 bc a7 10  2f e9 4b 0f c4 4f b1 f9  |\...I.../.K..O..|
000002f0  9d 6c 1c 92 39 80 27 59  7d 7d c9 db aa 45 3f a5  |.l..9.'Y}}...E?.|
00000300  fe ae 31 09 a7 7e 5d 09  42 32 4d 4a 00 00        |..1..~].B2MJ..|
0000030e
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 2 Handshake CRYPTO(0x06) offset=2884 len=264
Ordered CRYPTO data in Handshake crypto level
00000000  0f 00 01 04 08 04 01 00  6c 31 0a 8f 41 9f 3e 05  |........l1..A.>.|
00000010  e0 47 86 ea 30 ee 48 6b  b3 6b e0 b8 94 7d ed ea  |.G..0.Hk.k...}..|
00000020  fb 43 e7 2d 6c 74 85 4c  7e 53 48 98 3f e5 38 a1  |.C.-lt.L~SH.?.8.|
00000030  f0 24 ec 5c c1 a2 bd c0  75 82 79 12 44 32 da 02  |.$.\....u.y.D2..|
00000040  f3 cc be 0a 38 6b 0d 4a  f5 2c e1 b9 3b bc e9 88  |....8k.J.,..;...|
00000050  a1 32 af db fa e0 f4 89  ab 17 8d 98 86 1f 0c 4f  |.2.............O|
00000060  d2 0d b7 df ac 2d 06 0c  2f de 3b 84 06 eb d4 1a  |.....-../.;.....|
00000070  89 73 5f 09 0f 93 10 7c  ab 71 13 e5 bd a8 fc 45  |.s_....|.q.....E|
00000080  c1 c9 26 6e d7 6f a1 a9  62 d7 27 c2 17 78 ac ce  |..&n.o..b.'..x..|
00000090  10 52 75 38 a6 41 9f c0  d7 22 f2 6d be 55 0f ac  |.Ru8.A...".m.U..|
000000a0  34 0b 66 cb 02 09 d8 55  47 56 57 d1 de e1 44 2b  |4.f....UGVW...D+|
000000b0  1b 4b 01 20 3d 35 9d a0  02 50 a7 ad 0c f2 e1 d4  |.K. =5...P......|
000000c0  9e ae 07 6c 37 ea eb dc  65 50 39 15 e9 10 9d b3  |...l7...eP9.....|
000000d0  3f fa c5 d2 d9 86 57 d9  81 44 d0 a6 9b 75 b1 90  |?.....W..D...u..|
000000e0  0e 9c d0 34 3a d5 e0 72  91 df 18 de 2a 48 2e ab  |...4:..r....*H..|
000000f0  91 cd 17 a3 af 10 a1 21  fd 67 44 36 d9 45 94 97  |.......!.gD6.E..|
00000100  06 b7 64 44 9d 92 d2 1e                           |..dD....|
00000108
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 2 Handshake CRYPTO(0x06) offset=3148 len=36
Ordered CRYPTO data in Handshake crypto level
00000000  14 00 00 20 e8 5f fd f0  f5 38 85 b6 be 79 02 51  |... ._...8...y.Q|
00000010  16 5c f6 d3 19 e6 38 6b  ea 11 dd db 1c 38 e4 a4  |.\....8k.....8..|
00000020  84 c5 15 4b                                       |...K|
00000024
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters stateless_reset_token=0x96e2e580c563e3e0450866774cf18cbf
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters original_destination_connection_id=0xe42f745cd18b29f117c0d2d3e167bbab7623
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters initial_source_connection_id=******************************************
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters initial_max_stream_data_bidi_local=1048576
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters initial_max_stream_data_bidi_remote=1048576
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters initial_max_stream_data_uni=1048576
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters initial_max_data=137363456
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters initial_max_streams_bidi=128
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters initial_max_streams_uni=3
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters max_idle_timeout=30000
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters max_udp_payload_size=65527
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters ack_delay_exponent=3
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters max_ack_delay=25
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters active_connection_id_limit=2
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters disable_active_migration=0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters max_datagram_frame_size=0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cry remote transport_parameters grease_quic_bit=0
🔧 QPACK Settings: max_dtable_capacity=16384, encoder_max_dtable_capacity=16384
🔧 QPACK CONTEXT INIT: Set max_dtable_capacity = 16384 (was 0)
QPACK DEBUG: Initializing encoder with capacity 16384
🔧 QPACK CONTEXT INIT: Set max_dtable_capacity = 16384 (was 0)
🔧 QPACK: Encoder capacity fix applied at library level
http: control stream=2
http: QPACK streams encoder=6 decoder=a
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt read packet 1159 left 0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con processing buffered handshake packet
QUIC handshake has completed
Negotiated cipher suite is TLS13-AES128-GCM-SHA256
Negotiated ALPN is h3
http: stream 0x0 submit request headers
[:method: GET]
[:scheme: http]
[:authority: aliyun.hawks.top]
[:path: /]
[user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]
[cookie: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa;]
[x-custom-auth-token: eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee]
[authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjM0NTY3ODkwfQ.signature_data_for_qpack_testing_purposes_only_not_too_long]

=== QPACK DEBUG: Request #1 ===
QPACK: Long value header [user-agent]: 111 bytes, flags=0x06
QPACK: Long value header [cookie]: 185 bytes, flags=0x08
  -> TRY_INDEX flag set - should be indexed!
QPACK: Long value header [x-custom-auth-token]: 180 bytes, flags=0x08
  -> TRY_INDEX flag set - should be indexed!
QPACK: Long value header [authorization]: 178 bytes, flags=0x08
  -> TRY_INDEX flag set - should be indexed!
=== End QPACK DEBUG ===

http: stream 0x4 submit request headers
[:method: GET]
[:scheme: http]
[:authority: aliyun.hawks.top]
[:path: /]
[user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]
[cookie: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa;]
[x-custom-auth-token: eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee]
[authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjM0NTY3ODkwfQ.signature_data_for_qpack_testing_purposes_only_not_too_long]

=== QPACK DEBUG: Request #2 ===
QPACK: Long value header [user-agent]: 111 bytes, flags=0x06
QPACK: Long value header [cookie]: 185 bytes, flags=0x08
  -> TRY_INDEX flag set - should be indexed!
QPACK: Long value header [x-custom-auth-token]: 180 bytes, flags=0x08
  -> TRY_INDEX flag set - should be indexed!
QPACK: Long value header [authorization]: 178 bytes, flags=0x08
  -> TRY_INDEX flag set - should be indexed!
=== End QPACK DEBUG ===

http: stream 0x8 submit request headers
[:method: GET]
[:scheme: http]
[:authority: aliyun.hawks.top]
[:path: /]
[user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]
[cookie: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa;]
[x-custom-auth-token: eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee]
[authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjM0NTY3ODkwfQ.signature_data_for_qpack_testing_purposes_only_not_too_long]

=== QPACK DEBUG: Request #3 ===
QPACK: Long value header [user-agent]: 111 bytes, flags=0x06
QPACK: Long value header [cookie]: 185 bytes, flags=0x08
  -> TRY_INDEX flag set - should be indexed!
QPACK: Long value header [x-custom-auth-token]: 180 bytes, flags=0x08
  -> TRY_INDEX flag set - should be indexed!
QPACK: Long value header [authorization]: 178 bytes, flags=0x08
  -> TRY_INDEX flag set - should be indexed!
=== End QPACK DEBUG ===

********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con processing buffered protected packet
Set timer=0.010833s
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt tx pkn=586487896 dcid=****************************************** scid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 version=0x00000001 type=Initial len=0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487896 Initial ACK(0x02) largest_ack=1 ack_delay=0(0) ack_range_count=0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487896 Initial ACK(0x02) range=[1..0] len=1
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt tx pkn=586487894 dcid=****************************************** scid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 version=0x00000001 type=Handshake len=0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 Handshake ACK(0x02) largest_ack=2 ack_delay=0(0) ack_range_count=0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 Handshake ACK(0x02) range=[2..0] len=2
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 Handshake CRYPTO(0x06) offset=0 len=36
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=**************** timeout=13
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con discarding Initial packet number space
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=**************** timeout=6
Updating traffic key
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt tx pkn=586487894 dcid=****************************************** type=1RTT k=0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 1RTT NEW_CONNECTION_ID(0x18) seq=1 cid=0x772a5aff4c444e6907032bb1cd80bcc8cf retire_prior_to=0 stateless_reset_token=0xf5964fab220764c59fdf7cfcde65780c
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 1RTT STREAM(0x0a) id=0x2 fin=0 offset=0 len=20 uni=1
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 1RTT STREAM(0x0a) id=0xa fin=0 offset=0 len=1 uni=1
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 1RTT STREAM(0x0a) id=0x6 fin=0 offset=0 len=1 uni=1

*** QPACK_ENCODER_ENCODE CALLED ***
Stream ID: 0, Headers count: 8

=== QPACK ENCODER: Starting encode for stream 0, 8 headers ===
QPACK ENCODER: Dynamic table entries: 0/0, size: 0/16384 bytes

🔍 QPACK ENCODER: Processing header :method: GET (flags=0x06, len=7+3)
   📊 Allow blocking: NO
QPACK ENCODER: Token=1, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 42 <= threshold 12288 -> ALLOWING indexing
QPACK: Header :method:GET -> STORE (space=42, capacity=16384, token=1, flags=0x6)
QPACK ENCODER: Static table lookup result: index=17, name_value_match=1
   ✅ STATIC TABLE HIT: Exact match at index 17
   💾 Encoding as STATIC INDEXED (saves 10 bytes)

🔍 QPACK ENCODER: Processing header :scheme: http (flags=0x06, len=7+4)
   📊 Allow blocking: NO
QPACK ENCODER: Token=9, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 43 <= threshold 12288 -> ALLOWING indexing
QPACK: Header :scheme:http -> STORE (space=43, capacity=16384, token=9, flags=0x6)
QPACK ENCODER: Static table lookup result: index=22, name_value_match=1
   ✅ STATIC TABLE HIT: Exact match at index 22
   💾 Encoding as STATIC INDEXED (saves 11 bytes)

🔍 QPACK ENCODER: Processing header :authority: aliyun.hawks.top (flags=0x06, len=10+16)
   📊 Allow blocking: NO
QPACK ENCODER: Token=0, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 58 <= threshold 12288 -> ALLOWING indexing
QPACK: Header :authority:aliyun.hawks.top -> STORE (space=58, capacity=16384, token=0, flags=0x6)
QPACK ENCODER: Static table lookup result: index=0, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0xbbfa06de, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=-1
QPACK ENCODER: just_index=1 (indexing_mode=1, pb_index=-1)

🔍 QPACK ENCODER: Processing header :path: / (flags=0x06, len=5+1)
   📊 Allow blocking: NO
QPACK ENCODER: Token=8, static_entry=1, allow_blocking=0
QPACK: Header :path:/ -> LITERAL (token=8, no TRY_INDEX)
QPACK ENCODER: Static table lookup result: index=1, name_value_match=1
   ✅ STATIC TABLE HIT: Exact match at index 1
   💾 Encoding as STATIC INDEXED (saves 6 bytes)

🔍 QPACK ENCODER: Processing header user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 (flags=0x06, len=10+111)
   📊 Allow blocking: NO
QPACK ENCODER: Token=91, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 153 <= threshold 12288 -> ALLOWING indexing
QPACK: Header user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 -> STORE (space=153, capacity=16384, token=91, flags=0x6)
QPACK ENCODER: Static table lookup result: index=95, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0x24259bee, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=-1
QPACK ENCODER: just_index=1 (indexing_mode=1, pb_index=-1)

🔍 QPACK ENCODER: Processing header cookie: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa; (flags=0x08, len=6+185)
   📊 Allow blocking: NO
   🎯 TRY_INDEX flag detected - will attempt indexing
QPACK ENCODER: Token=68, static_entry=1, allow_blocking=0
   🍪 COOKIE long enough (185 >= 10 bytes) -> ALLOWING indexing (OPTIMIZED)
   📊 TRY_INDEX header: Using relaxed capacity threshold 14745 (90%)
   ✅ CAPACITY CHECK PASSED: space 223 <= threshold 14745 -> ALLOWING indexing
QPACK: Header cookie:aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa; -> STORE (space=223, capacity=16384, token=68, flags=0x8)
QPACK ENCODER: Static table lookup result: index=5, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0x77a740bf, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=-1
QPACK ENCODER: just_index=1 (indexing_mode=1, pb_index=-1)

🔍 QPACK ENCODER: Processing header x-custom-auth-token: eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee (flags=0x08, len=19+180)
   📊 Allow blocking: NO
   🎯 TRY_INDEX flag detected - will attempt indexing
QPACK ENCODER: Token=-1, static_entry=0, allow_blocking=0
   📊 TRY_INDEX header: Using relaxed capacity threshold 14745 (90%)
   ✅ CAPACITY CHECK PASSED: space 231 <= threshold 14745 -> ALLOWING indexing
QPACK: Header x-custom-auth-token:eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee -> STORE (space=231, capacity=16384, token=-1, flags=0x8)
QPACK ENCODER: No static table lookup (not a static entry)
QPACK ENCODER: Looking up in dynamic table (hash=0xe1abe8e6, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=-1
QPACK ENCODER: just_index=1 (indexing_mode=1, pb_index=-1)
   📝 ADDING TO DYNAMIC TABLE: x-custom-auth-token:eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee
   📊 Table space needed: 231 bytes
   🎯 TRY_INDEX header successfully indexed!
   📄 LITERAL ENCODING: x-custom-auth-token:eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee (not indexed)
   ⚠️  No compression benefit - full header transmitted

🔍 QPACK ENCODER: Processing header authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjM0NTY3ODkwfQ.signature_data_for_qpack_testing_purposes_only_not_too_long (flags=0x08, len=13+178)
   📊 Allow blocking: NO
   🎯 TRY_INDEX flag detected - will attempt indexing
QPACK ENCODER: Token=45, static_entry=1, allow_blocking=0
   🔐 AUTHORIZATION with TRY_INDEX -> ALLOWING indexing (OPTIMIZED)
   📊 TRY_INDEX header: Using relaxed capacity threshold 14745 (90%)
   ✅ CAPACITY CHECK PASSED: space 223 <= threshold 14745 -> ALLOWING indexing
QPACK: Header authorization:Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjM0NTY3ODkwfQ.signature_data_for_qpack_testing_purposes_only_not_too_long -> STORE (space=223, capacity=16384, token=45, flags=0x8)
QPACK ENCODER: Static table lookup result: index=84, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0x913657be, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=-1
QPACK ENCODER: just_index=1 (indexing_mode=1, pb_index=-1)

*** QPACK ENCODE COMPLETE ***
Stream 0: Header block size: 493 bytes (actual wire size)
Prefix size: 2 bytes, Encoder stream: 489 bytes
Total encoded size: 984 bytes, Headers: 8
*** END QPACK ENCODE ***

********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 1RTT STREAM(0x0e) id=0x6 fin=0 offset=1 len=489 uni=1
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487894 1RTT STREAM(0x0a) id=0x0 fin=0 offset=0 len=410 uni=0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=**************** timeout=6
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con sending PMTUD probe packet len=1406
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt tx pkn=586487895 dcid=****************************************** type=1RTT k=0
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487895 1RTT PING(0x01)
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487895 1RTT PADDING(0x00) len=1364
********* 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=**************** timeout=6
Sent packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 1200 bytes
Sent packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 1406 bytes
Timer has already expired: 0.000284s
I00000012 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt tx pkn=586487896 dcid=****************************************** type=1RTT k=0
I00000012 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487896 1RTT STREAM(0x0f) id=0x0 fin=1 offset=410 len=88 uni=0

*** QPACK_ENCODER_ENCODE CALLED ***
Stream ID: 4, Headers count: 8

=== QPACK ENCODER: Starting encode for stream 4, 8 headers ===
QPACK ENCODER: Dynamic table entries: 5/128, size: 888/16384 bytes

🔍 QPACK ENCODER: Processing header :method: GET (flags=0x06, len=7+3)
   📊 Allow blocking: NO
QPACK ENCODER: Token=1, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 42 <= threshold 12288 -> ALLOWING indexing
QPACK: Header :method:GET -> STORE (space=42, capacity=16384, token=1, flags=0x6)
QPACK ENCODER: Static table lookup result: index=17, name_value_match=1
   ✅ STATIC TABLE HIT: Exact match at index 17
   💾 Encoding as STATIC INDEXED (saves 10 bytes)

🔍 QPACK ENCODER: Processing header :scheme: http (flags=0x06, len=7+4)
   📊 Allow blocking: NO
QPACK ENCODER: Token=9, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 43 <= threshold 12288 -> ALLOWING indexing
QPACK: Header :scheme:http -> STORE (space=43, capacity=16384, token=9, flags=0x6)
QPACK ENCODER: Static table lookup result: index=22, name_value_match=1
   ✅ STATIC TABLE HIT: Exact match at index 22
   💾 Encoding as STATIC INDEXED (saves 11 bytes)

🔍 QPACK ENCODER: Processing header :authority: aliyun.hawks.top (flags=0x06, len=10+16)
   📊 Allow blocking: NO
QPACK ENCODER: Token=0, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 58 <= threshold 12288 -> ALLOWING indexing
QPACK: Header :authority:aliyun.hawks.top -> STORE (space=58, capacity=16384, token=0, flags=0x6)
QPACK ENCODER: Static table lookup result: index=0, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0xbbfa06de, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=0
QPACK ENCODER: just_index=0 (indexing_mode=1, pb_index=0)

🔍 QPACK ENCODER: Processing header :path: / (flags=0x06, len=5+1)
   📊 Allow blocking: NO
QPACK ENCODER: Token=8, static_entry=1, allow_blocking=0
QPACK: Header :path:/ -> LITERAL (token=8, no TRY_INDEX)
QPACK ENCODER: Static table lookup result: index=1, name_value_match=1
   ✅ STATIC TABLE HIT: Exact match at index 1
   💾 Encoding as STATIC INDEXED (saves 6 bytes)

🔍 QPACK ENCODER: Processing header user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 (flags=0x06, len=10+111)
   📊 Allow blocking: NO
QPACK ENCODER: Token=91, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 153 <= threshold 12288 -> ALLOWING indexing
QPACK: Header user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 -> STORE (space=153, capacity=16384, token=91, flags=0x6)
QPACK ENCODER: Static table lookup result: index=95, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0x24259bee, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=1
QPACK ENCODER: just_index=0 (indexing_mode=1, pb_index=1)

🔍 QPACK ENCODER: Processing header cookie: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa; (flags=0x08, len=6+185)
   📊 Allow blocking: NO
   🎯 TRY_INDEX flag detected - will attempt indexing
QPACK ENCODER: Token=68, static_entry=1, allow_blocking=0
   🍪 COOKIE long enough (185 >= 10 bytes) -> ALLOWING indexing (OPTIMIZED)
   📊 TRY_INDEX header: Using relaxed capacity threshold 14745 (90%)
   ✅ CAPACITY CHECK PASSED: space 223 <= threshold 14745 -> ALLOWING indexing
QPACK: Header cookie:aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa; -> STORE (space=223, capacity=16384, token=68, flags=0x8)
QPACK ENCODER: Static table lookup result: index=5, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0x77a740bf, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=2
QPACK ENCODER: just_index=0 (indexing_mode=1, pb_index=2)

🔍 QPACK ENCODER: Processing header x-custom-auth-token: eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee (flags=0x08, len=19+180)
   📊 Allow blocking: NO
   🎯 TRY_INDEX flag detected - will attempt indexing
QPACK ENCODER: Token=-1, static_entry=0, allow_blocking=0
   📊 TRY_INDEX header: Using relaxed capacity threshold 14745 (90%)
   ✅ CAPACITY CHECK PASSED: space 231 <= threshold 14745 -> ALLOWING indexing
QPACK: Header x-custom-auth-token:eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee -> STORE (space=231, capacity=16384, token=-1, flags=0x8)
QPACK ENCODER: No static table lookup (not a static entry)
QPACK ENCODER: Looking up in dynamic table (hash=0xe1abe8e6, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=3
QPACK ENCODER: just_index=0 (indexing_mode=1, pb_index=3)
   📄 LITERAL ENCODING: x-custom-auth-token:eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee (not indexed)
   ⚠️  No compression benefit - full header transmitted

🔍 QPACK ENCODER: Processing header authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjM0NTY3ODkwfQ.signature_data_for_qpack_testing_purposes_only_not_too_long (flags=0x08, len=13+178)
   📊 Allow blocking: NO
   🎯 TRY_INDEX flag detected - will attempt indexing
QPACK ENCODER: Token=45, static_entry=1, allow_blocking=0
   🔐 AUTHORIZATION with TRY_INDEX -> ALLOWING indexing (OPTIMIZED)
   📊 TRY_INDEX header: Using relaxed capacity threshold 14745 (90%)
   ✅ CAPACITY CHECK PASSED: space 223 <= threshold 14745 -> ALLOWING indexing
QPACK: Header authorization:Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjM0NTY3ODkwfQ.signature_data_for_qpack_testing_purposes_only_not_too_long -> STORE (space=223, capacity=16384, token=45, flags=0x8)
QPACK ENCODER: Static table lookup result: index=84, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0x913657be, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=4
QPACK ENCODER: just_index=0 (indexing_mode=1, pb_index=4)

*** QPACK ENCODE COMPLETE ***
Stream 4: Header block size: 493 bytes (actual wire size)
Prefix size: 2 bytes, Encoder stream: 0 bytes
Total encoded size: 495 bytes, Headers: 8
*** END QPACK ENCODE ***

I00000012 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487896 1RTT STREAM(0x0b) id=0x4 fin=1 offset=0 len=498 uni=0

*** QPACK_ENCODER_ENCODE CALLED ***
Stream ID: 8, Headers count: 8

=== QPACK ENCODER: Starting encode for stream 8, 8 headers ===
QPACK ENCODER: Dynamic table entries: 5/128, size: 888/16384 bytes

🔍 QPACK ENCODER: Processing header :method: GET (flags=0x06, len=7+3)
   📊 Allow blocking: NO
QPACK ENCODER: Token=1, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 42 <= threshold 12288 -> ALLOWING indexing
QPACK: Header :method:GET -> STORE (space=42, capacity=16384, token=1, flags=0x6)
QPACK ENCODER: Static table lookup result: index=17, name_value_match=1
   ✅ STATIC TABLE HIT: Exact match at index 17
   💾 Encoding as STATIC INDEXED (saves 10 bytes)

🔍 QPACK ENCODER: Processing header :scheme: http (flags=0x06, len=7+4)
   📊 Allow blocking: NO
QPACK ENCODER: Token=9, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 43 <= threshold 12288 -> ALLOWING indexing
QPACK: Header :scheme:http -> STORE (space=43, capacity=16384, token=9, flags=0x6)
QPACK ENCODER: Static table lookup result: index=22, name_value_match=1
   ✅ STATIC TABLE HIT: Exact match at index 22
   💾 Encoding as STATIC INDEXED (saves 11 bytes)

🔍 QPACK ENCODER: Processing header :authority: aliyun.hawks.top (flags=0x06, len=10+16)
   📊 Allow blocking: NO
QPACK ENCODER: Token=0, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 58 <= threshold 12288 -> ALLOWING indexing
QPACK: Header :authority:aliyun.hawks.top -> STORE (space=58, capacity=16384, token=0, flags=0x6)
QPACK ENCODER: Static table lookup result: index=0, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0xbbfa06de, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=0
QPACK ENCODER: just_index=0 (indexing_mode=1, pb_index=0)

🔍 QPACK ENCODER: Processing header :path: / (flags=0x06, len=5+1)
   📊 Allow blocking: NO
QPACK ENCODER: Token=8, static_entry=1, allow_blocking=0
QPACK: Header :path:/ -> LITERAL (token=8, no TRY_INDEX)
QPACK ENCODER: Static table lookup result: index=1, name_value_match=1
   ✅ STATIC TABLE HIT: Exact match at index 1
   💾 Encoding as STATIC INDEXED (saves 6 bytes)

🔍 QPACK ENCODER: Processing header user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 (flags=0x06, len=10+111)
   📊 Allow blocking: NO
QPACK ENCODER: Token=91, static_entry=1, allow_blocking=0
   📊 Regular header: Using standard capacity threshold 12288 (75%)
   ✅ CAPACITY CHECK PASSED: space 153 <= threshold 12288 -> ALLOWING indexing
QPACK: Header user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 -> STORE (space=153, capacity=16384, token=91, flags=0x6)
QPACK ENCODER: Static table lookup result: index=95, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0x24259bee, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=1
QPACK ENCODER: just_index=0 (indexing_mode=1, pb_index=1)

🔍 QPACK ENCODER: Processing header cookie: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa; (flags=0x08, len=6+185)
   📊 Allow blocking: NO
   🎯 TRY_INDEX flag detected - will attempt indexing
QPACK ENCODER: Token=68, static_entry=1, allow_blocking=0
   🍪 COOKIE long enough (185 >= 10 bytes) -> ALLOWING indexing (OPTIMIZED)
   📊 TRY_INDEX header: Using relaxed capacity threshold 14745 (90%)
   ✅ CAPACITY CHECK PASSED: space 223 <= threshold 14745 -> ALLOWING indexing
QPACK: Header cookie:aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa; -> STORE (space=223, capacity=16384, token=68, flags=0x8)
QPACK ENCODER: Static table lookup result: index=5, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0x77a740bf, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=2
QPACK ENCODER: just_index=0 (indexing_mode=1, pb_index=2)

🔍 QPACK ENCODER: Processing header x-custom-auth-token: eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee (flags=0x08, len=19+180)
   📊 Allow blocking: NO
   🎯 TRY_INDEX flag detected - will attempt indexing
QPACK ENCODER: Token=-1, static_entry=0, allow_blocking=0
   📊 TRY_INDEX header: Using relaxed capacity threshold 14745 (90%)
   ✅ CAPACITY CHECK PASSED: space 231 <= threshold 14745 -> ALLOWING indexing
QPACK: Header x-custom-auth-token:eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee -> STORE (space=231, capacity=16384, token=-1, flags=0x8)
QPACK ENCODER: No static table lookup (not a static entry)
QPACK ENCODER: Looking up in dynamic table (hash=0xe1abe8e6, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=3
QPACK ENCODER: just_index=0 (indexing_mode=1, pb_index=3)
   📄 LITERAL ENCODING: x-custom-auth-token:eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee (not indexed)
   ⚠️  No compression benefit - full header transmitted

🔍 QPACK ENCODER: Processing header authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjM0NTY3ODkwfQ.signature_data_for_qpack_testing_purposes_only_not_too_long (flags=0x08, len=13+178)
   📊 Allow blocking: NO
   🎯 TRY_INDEX flag detected - will attempt indexing
QPACK ENCODER: Token=45, static_entry=1, allow_blocking=0
   🔐 AUTHORIZATION with TRY_INDEX -> ALLOWING indexing (OPTIMIZED)
   📊 TRY_INDEX header: Using relaxed capacity threshold 14745 (90%)
   ✅ CAPACITY CHECK PASSED: space 223 <= threshold 14745 -> ALLOWING indexing
QPACK: Header authorization:Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjM0NTY3ODkwfQ.signature_data_for_qpack_testing_purposes_only_not_too_long -> STORE (space=223, capacity=16384, token=45, flags=0x8)
QPACK ENCODER: Static table lookup result: index=84, name_value_match=0
QPACK ENCODER: Looking up in dynamic table (hash=0x913657be, indexing_mode=1)
QPACK ENCODER: Dynamic table lookup result: index=-1, name_value_match=0, pb_index=4
QPACK ENCODER: just_index=0 (indexing_mode=1, pb_index=4)

*** QPACK ENCODE COMPLETE ***
Stream 8: Header block size: 493 bytes (actual wire size)
Prefix size: 2 bytes, Encoder stream: 0 bytes
Total encoded size: 495 bytes, Headers: 8
*** END QPACK ENCODE ***

I00000012 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm tx 586487896 1RTT STREAM(0x0b) id=0x8 fin=1 offset=0 len=498 uni=0
I00000012 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=**************** timeout=6
Sent packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 1139 bytes
Timer has already expired: 0.000344s
Set timer=0.005726s
Received packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 71 bytes
I00000012 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con recv packet len=71
I00000012 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt delayed Initial packet was discarded
I00000012 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt read packet 71 left 0
Set timer=0.005686s
Set timer=0.005677s
Received packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 65 bytes
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con recv packet len=65
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt rx pkn=0 dcid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 type=1RTT k=0
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 0 1RTT ACK(0x02) largest_ack=586487894 ack_delay=0(0) ack_range_count=0
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 0 1RTT ACK(0x02) range=[586487894..586487894] len=0
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc latest_rtt=2 min_rtt=2 smoothed_rtt=2 rttvar=0 ack_delay=0
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 cca 1017 bytes acked, slow start cwnd=14217
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=**************** timeout=3
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 0 1RTT STREAM(0x0e) id=0x3 fin=0 offset=0 len=1 uni=1
Ordered STREAM data stream_id=0x3
00000000  00                                                |.|
00000001
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 0 1RTT STREAM(0x0e) id=0x3 fin=0 offset=1 len=8 uni=1
Ordered STREAM data stream_id=0x3
00000000  04 06 01 50 00 07 40 80                           |...P..@.|
00000008
🔧 QPACK: nghttp3_qpack_encoder_set_max_dtable_capacity called with 4096
🔧 QPACK: Current max_dtable_capacity = 16384, hard_max = 16384
🔧 QPACK: After min check, max_dtable_capacity = 4096
🔧 QPACK: Set max_dtable_capacity to 4096 (immediate)
🔧 QPACK: Final state - max_dtable_capacity = 4096, last_max_dtable_update = 4096
http: remote settings
http: SETTINGS_MAX_FIELD_SECTION_SIZE=4611686018427387903
http: SETTINGS_QPACK_MAX_TABLE_CAPACITY=4096
http: SETTINGS_QPACK_BLOCKED_STREAMS=128
http: SETTINGS_ENABLE_CONNECT_PROTOCOL=0
http: SETTINGS_H3_DATAGRAM=0
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 0 1RTT STREAM(0x0e) id=0x7 fin=0 offset=0 len=1 uni=1
Ordered STREAM data stream_id=0x7
00000000  03                                                |.|
00000001
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt read packet 65 left 0
Received packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 891 bytes
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con recv packet len=891
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt rx pkn=1 dcid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 type=1RTT k=0
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 1RTT CRYPTO(0x06) offset=0 len=321
Ordered CRYPTO data in 1-RTT crypto level
00000000  04 00 01 3d 00 01 51 80  af 88 49 c1 08 00 00 00  |...=..Q...I.....|
00000010  00 00 00 00 00 01 20 c7  f3 15 16 ca 6f 94 ed 9e  |...... .....o...|
00000020  9e 8e b7 f6 f1 53 b1 83  e9 44 d4 a4 95 5e 44 e9  |.....S...D...^D.|
00000030  7c 41 9b d2 ce a4 a0 1e  9a 09 59 ce 74 ef e4 56  ||A........Y.t..V|
00000040  44 c0 3f 06 be 5f 86 8e  a2 a6 1a 54 88 11 90 38  |D.?.._.....T...8|
00000050  01 5d 11 30 81 2d ae 05  34 25 5d d9 54 14 f8 23  |.].0.-..4%].T..#|
00000060  52 e5 f3 b4 62 d8 ce 47  ea 4e c4 ae 50 f6 af b2  |R...b..G.N..P...|
00000070  0c 7f d0 54 47 5e 1f 90  28 85 7d 54 78 34 18 9e  |...TG^..(.}Tx4..|
00000080  29 7f ef 00 f7 16 c3 33  e3 19 c5 07 09 15 15 10  |)......3........|
00000090  98 a6 6a 6a 47 f2 8c 8f  b6 d3 bb 79 a2 0a 18 36  |..jjG......y...6|
000000a0  34 1a e2 c2 de bc e3 3e  87 5b ea 43 34 39 8f 81  |4......>.[.C49..|
000000b0  ff 7d f4 65 d6 88 6e a9  be e9 3a cb 33 9b ea 7a  |.}.e..n...:.3..z|
000000c0  67 5d c7 60 bc d9 40 4d  2a 4b 0a ca 27 70 60 58  |g].`..@M*K..'p`X|
000000d0  6e cb 52 e0 d1 78 41 b9  e5 d4 e4 29 a4 c7 bb 43  |n.R..xA....)...C|
000000e0  d9 41 a9 4f 96 39 7e 21  79 b6 ce 48 5e 78 14 b7  |.A.O.9~!y..H^x..|
000000f0  14 65 06 cb e8 99 60 47  b8 ab b3 b0 f5 bd 0d 69  |.e....`G.......i|
00000100  d5 42 3d e3 6d a9 2b d0  49 9e 85 82 35 57 d1 ec  |.B=.m.+.I...5W..|
00000110  fe ad cb 19 45 09 b3 80  d0 d1 11 4b bd ef 7d 40  |....E......K..}@|
00000120  96 02 2d fc 7f 74 04 61  29 4e e8 38 a6 4f 8e 2a  |..-..t.a)N.8.O.*|
00000130  c2 6c d6 28 fa 67 fc 00  08 00 2a 00 04 ff ff ff  |.l.(.g....*.....|
00000140  ff                                                |.|
00000141
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 1RTT CRYPTO(0x06) offset=321 len=321
Ordered CRYPTO data in 1-RTT crypto level
00000000  04 00 01 3d 00 01 51 80  1b 65 b3 bc 08 00 00 00  |...=..Q..e......|
00000010  00 00 00 00 01 01 20 c7  f3 15 16 ca 6f 94 ed 9e  |...... .....o...|
00000020  9e 8e b7 f6 f1 53 b1 67  f8 a6 59 cb 71 a3 cb 00  |.....S.g..Y.q...|
00000030  1c 6e 81 24 24 f9 dd 75  47 44 0e 6e 2f 9b 59 bf  |.n.$$..uGD.n/.Y.|
00000040  bc ed 03 b4 a1 64 ac b8  16 7a 10 13 8d 6c e5 63  |.....d...z...l.c|
00000050  41 05 de e5 35 4c 1f c6  14 d2 e3 16 f1 7b cd c2  |A...5L.......{..|
00000060  8e bd e1 d8 73 59 2c 6d  63 be 5b 8e cd 59 e9 fd  |....sY,mc.[..Y..|
00000070  29 be 24 2f ef f4 70 f5  e5 87 3d 05 fb 65 a8 91  |).$/..p...=..e..|
00000080  3a 92 29 f6 e7 74 2e 0f  5c 44 f8 ad 56 54 18 70  |:.)..t..\D..VT.p|
00000090  6b 76 05 90 2a 68 51 9b  d2 fa 25 a0 20 de d5 37  |kv..*hQ...%. ..7|
000000a0  95 2e e0 b4 1b 78 e3 36  87 29 d0 ba f3 6c b6 66  |.....x.6.)...l.f|
000000b0  cb 89 d5 ba 41 ea de 19  f7 cb 98 80 94 31 75 a5  |....A........1u.|
000000c0  46 f6 f4 86 34 89 79 f7  42 81 23 6f 4c 3c 94 59  |F...4.y.B.#oL<.Y|
000000d0  5b d4 31 10 e8 dd 1c 26  d9 f0 e3 07 1a 0a d2 51  |[.1....&.......Q|
000000e0  c6 8a e0 a8 a1 12 45 0f  1a 2d 34 cc 8e 81 0d 25  |......E..-4....%|
000000f0  36 61 81 a4 ff 25 8c cf  96 db 76 df 92 ca 6b 64  |6a...%....v...kd|
00000100  0e 59 4d 46 cd 87 5c 8a  70 7d 1e dc 17 34 41 63  |.YMF..\.p}...4Ac|
00000110  91 29 d5 4f 94 13 8a d5  5e 0f 4b 16 4a 8a a8 ff  |.).O....^.K.J...|
00000120  6e 61 a8 67 06 f8 55 49  c4 38 65 cc 78 aa 75 cf  |na.g..UI.8e.x.u.|
00000130  4f fa 82 32 cd 41 34 00  08 00 2a 00 04 ff ff ff  |O..2.A4...*.....|
00000140  ff                                                |.|
00000141
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 1RTT HANDSHAKE_DONE(0x1e)
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con discarding Handshake packet number space
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=**************** timeout=28
QUIC handshake has been confirmed
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 ldc loss_detection_timer=**************** timeout=28
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 1RTT NEW_CONNECTION_ID(0x18) seq=1 cid=0x54aa86dae49300000005ae8ab5f5fbe527bd6d05 retire_prior_to=0 stateless_reset_token=0xf51b518a545b9906c8f8fb5495ad71bc
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 1RTT NEW_CONNECTION_ID(0x18) seq=2 cid=0x54aa86dae49300000005ae8ab5f5ac3e7d47d0a1 retire_prior_to=0 stateless_reset_token=0x1ca9d777d8b5596cb322fdbef81a0910
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 1RTT NEW_CONNECTION_ID(0x18) seq=3 cid=0x54aa86dae49300000005ae8ab5f573cbf97a2852 retire_prior_to=0 stateless_reset_token=0x243c61c20c9f2634206f73547443d062
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 1RTT NEW_CONNECTION_ID(0x18) seq=4 cid=0x54aa86dae49300000005ae8ab5f5873fb442564d retire_prior_to=0 stateless_reset_token=0x58deac2af3d1eacf85999e24c04cd16d
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 1RTT NEW_CONNECTION_ID(0x18) seq=5 cid=0x54aa86dae49300000005ae8ab5f566a890df85b8 retire_prior_to=0 stateless_reset_token=0xd53f8a906c6cd3278e07a71a42e41c00
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 1 1RTT STOP_SENDING(0x05) id=0x6 app_error_code=(unknown)(0x100)
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt read packet 891 left 0
Received packet: local=[********]:35652 remote=[**************]:443 ecn=0x0 51 bytes
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 con recv packet len=51
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 pkt rx pkn=2 dcid=0xf3aa8d4925f045ee7b8e0a682029f8ff97 type=1RTT k=0
I00000014 0xf3aa8d4925f045ee7b8e0a682029f8ff97 frm rx 2 1RTT CONNECTION_CLOSE(0x1d) error_code=(unknown)(0x201) frame_type=0 reason_len=12 reason=[stream error]
ngtcp2_conn_read_pkt: ERR_DRAINING
