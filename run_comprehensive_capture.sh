#!/bin/bash

echo "=== 综合 QPACK 抓包测试（所有接口） ==="

# 设置环境变量
export SSLKEYLOGFILE=/home/<USER>/ngtcp2_qpack/quic_keylog_file

# 清理之前的文件
rm -f /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap
rm -f /home/<USER>/ngtcp2_qpack/quic_keylog_file

echo "1. 网络接口信息..."
echo "   可用接口:"
ip link show | grep -E "^[0-9]+:" | awk '{print "     " $2}' | sed 's/://'

echo ""
echo "2. 路由信息..."
echo "   默认路由:"
ip route | grep default | sed 's/^/     /'

echo ""
echo "3. 解析目标主机..."
TARGET_HOST="aliyun.hawks.top"
TARGET_IP=$(nslookup $TARGET_HOST | grep -A1 "Name:" | grep "Address:" | awk '{print $2}' | head -1)
echo "   目标主机: $TARGET_HOST"
echo "   目标 IP: $TARGET_IP"

echo ""
echo "4. 启动全接口抓包..."
# 使用 any 接口捕获所有流量，但使用更宽泛的过滤器
sudo tcpdump -i any -w /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap \
    -s 0 "port 443" &
TCPDUMP_PID=$!

echo "   抓包进程 PID: $TCPDUMP_PID"
echo "   过滤条件: port 443 (所有 HTTPS/QUIC 流量)"

echo ""
echo "5. 等待抓包启动..."
sleep 3

echo "6. 运行 QPACK 测试..."
cd /home/<USER>/ngtcp2_qpack/ngtcp2/examples

echo "   开始发送 HTTP/3 请求..."
timeout 45 ./wsslclient -n 3 aliyun.hawks.top 443 http://aliyun.hawks.top/ \
    > qpack_comprehensive_test.log 2>&1

echo ""
echo "7. 等待数据传输完成..."
sleep 5

echo "8. 停止抓包..."
sudo kill $TCPDUMP_PID 2>/dev/null
sleep 2

echo ""
echo "9. 验证生成的文件..."
if [ -f /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap ]; then
    PCAP_SIZE=$(ls -lh /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap | awk '{print $5}')
    echo "   ✅ 抓包文件大小: $PCAP_SIZE"
    
    # 检查数据包数量
    if command -v capinfos &> /dev/null; then
        PACKET_COUNT=$(capinfos /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap 2>/dev/null | grep "Number of packets" | awk '{print $4}')
        echo "   📦 数据包数量: $PACKET_COUNT"
        
        if [ "$PACKET_COUNT" -gt 0 ]; then
            echo "   🎉 成功捕获到数据包！"
            
            # 显示前几个数据包的基本信息
            echo ""
            echo "   📋 前5个数据包预览:"
            tshark -r /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap -c 5 -T fields \
                -e frame.number -e ip.src -e ip.dst -e udp.srcport -e udp.dstport -e frame.len \
                2>/dev/null | sed 's/^/      /' || echo "      无法解析数据包"
        else
            echo "   ⚠️  抓包文件为空"
        fi
    fi
else
    echo "   ❌ 抓包文件未生成"
fi

if [ -f /home/<USER>/ngtcp2_qpack/quic_keylog_file ]; then
    KEYLOG_SIZE=$(ls -lh /home/<USER>/ngtcp2_qpack/quic_keylog_file | awk '{print $5}')
    KEYLOG_LINES=$(wc -l < /home/<USER>/ngtcp2_qpack/quic_keylog_file)
    echo "   ✅ 密钥文件大小: $KEYLOG_SIZE"
    echo "   🔑 密钥文件行数: $KEYLOG_LINES"
    
    if [ $KEYLOG_LINES -gt 0 ]; then
        echo ""
        echo "   🔍 密钥类型统计:"
        grep -o "^[A-Z_]*" /home/<USER>/ngtcp2_qpack/quic_keylog_file | sort | uniq -c | sed 's/^/      /'
    fi
else
    echo "   ❌ 密钥文件未生成"
fi

echo ""
echo "10. QPACK 压缩效果验证..."
if [ -f qpack_comprehensive_test.log ]; then
    echo "    📊 编码大小对比:"
    grep "Total encoded size" qpack_comprehensive_test.log | sed 's/^/       /'
    
    echo ""
    echo "    🎯 动态表建立:"
    grep "ADDING TO DYNAMIC TABLE" qpack_comprehensive_test.log | wc -l | sed 's/^/       动态表条目数: /'
    
    echo ""
    echo "    ✅ 容量检查结果:"
    grep "CAPACITY CHECK PASSED" qpack_comprehensive_test.log | wc -l | sed 's/^/       通过的检查数: /'
fi

echo ""
echo "=== 综合抓包测试完成 ==="
echo "📁 生成的文件:"
echo "   抓包文件: /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap"
echo "   密钥文件: /home/<USER>/ngtcp2_qpack/quic_keylog_file"
echo "   测试日志: /home/<USER>/ngtcp2_qpack/ngtcp2/examples/qpack_comprehensive_test.log"

# 如果捕获到数据包，提供解密分析命令
if [ -f /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap ]; then
    PACKET_COUNT=$(capinfos /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap 2>/dev/null | grep "Number of packets" | awk '{print $4}')
    if [ "$PACKET_COUNT" -gt 0 ]; then
        echo ""
        echo "🔍 解密分析命令:"
        echo "   # 查看 QUIC 数据包:"
        echo "   tshark -r /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap \\"
        echo "     -o tls.keylog_file:/home/<USER>/ngtcp2_qpack/quic_keylog_file \\"
        echo "     -Y \"quic\" -c 10"
        echo ""
        echo "   # 查看解密后的 HTTP/3 数据:"
        echo "   tshark -r /home/<USER>/ngtcp2_qpack/qpack_comprehensive.pcap \\"
        echo "     -o tls.keylog_file:/home/<USER>/ngtcp2_qpack/quic_keylog_file \\"
        echo "     -Y \"http3\" -V"
    fi
fi
