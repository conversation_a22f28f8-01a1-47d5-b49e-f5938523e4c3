#!/bin/bash

echo "=== QPACK 动态表扩展性抓包测试（wsslclient2） ==="

# 设置环境变量和文件路径
export SSLKEYLOGFILE=/home/<USER>/ngtcp2_qpack/qpack_scalability_keylog.log
CAPTURE_FILE="/home/<USER>/ngtcp2_qpack/qpack_scalability_test.pcap"
LOG_FILE="/home/<USER>/ngtcp2_qpack/ngtcp2/examples/qpack_scalability_capture_test.log"
TARGET_HOST="aliyun.hawks.top"
TARGET_IP=$(dig +short $TARGET_HOST | head -1)

# 清理之前的文件
rm -f "$CAPTURE_FILE"
rm -f "$SSLKEYLOGFILE"
rm -f "$LOG_FILE"

echo "1. 网络接口信息..."
echo "   可用接口:"
ip link show | grep -E "^[0-9]+:" | awk '{print "     " $2}' | sed 's/://'

echo ""
echo "2. 路由信息..."
echo "   默认路由:"
ip route | grep default | sed 's/^/     /'

echo ""
echo "3. 解析目标主机..."
echo "   目标主机: $TARGET_HOST"
echo "   目标 IP: $TARGET_IP"

echo ""
echo "4. 启动 QPACK 扩展性测试抓包..."
# 使用 any 接口捕获所有流量，专门针对 QUIC/HTTP3 流量
sudo tcpdump -i any -w "$CAPTURE_FILE" \
    -s 0 "host $TARGET_HOST and port 443" &
TCPDUMP_PID=$!

echo "   抓包进程 PID: $TCPDUMP_PID"
echo "   过滤条件: host $TARGET_HOST and port 443 (QUIC/HTTP3 流量)"
echo "   捕获文件: $CAPTURE_FILE"

echo ""
echo "5. 等待抓包启动..."
sleep 3

echo "6. 运行 QPACK 动态表扩展性测试..."
cd /home/<USER>/ngtcp2_qpack/ngtcp2/examples

# 验证 wsslclient2 存在
if [ ! -f "./wsslclient2" ]; then
    echo "   ❌ wsslclient2 不存在，请先编译"
    sudo kill $TCPDUMP_PID 2>/dev/null
    exit 1
fi

echo "   🚀 开始 QPACK 扩展性测试..."
echo "   客户端: wsslclient2"
echo "   请求数量: 12 个（头部数量: 1→2→4→8→16→24→32→40→48→56→200→200）"
echo "   目标: 验证 200 个相同头部的压缩效果"
echo ""

# 记录测试开始时间
echo "=== QPACK 动态表扩展性测试开始 ===" > "$LOG_FILE"
echo "测试时间: $(date)" >> "$LOG_FILE"
echo "客户端: wsslclient2" >> "$LOG_FILE"
echo "请求数量: 12" >> "$LOG_FILE"
echo "目标服务器: $TARGET_HOST ($TARGET_IP)" >> "$LOG_FILE"
echo "捕获文件: $CAPTURE_FILE" >> "$LOG_FILE"
echo "密钥文件: $SSLKEYLOGFILE" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

# 运行 wsslclient2 进行扩展性测试
timeout 60 ./wsslclient2 -n 12 aliyun.hawks.top 443 http://aliyun.hawks.top/ \
    >> "$LOG_FILE" 2>&1

TEST_EXIT_CODE=$?
echo ""
echo "   测试完成，退出码: $TEST_EXIT_CODE"

echo ""
echo "7. 等待数据传输完成..."
sleep 5

echo "8. 停止抓包..."
sudo kill $TCPDUMP_PID 2>/dev/null
sleep 2

# 记录测试结束时间
echo "" >> "$LOG_FILE"
echo "=== QPACK 动态表扩展性测试结束 ===" >> "$LOG_FILE"
echo "结束时间: $(date)" >> "$LOG_FILE"

echo ""
echo "9. 验证生成的文件..."
if [ -f "$CAPTURE_FILE" ]; then
    PCAP_SIZE=$(ls -lh "$CAPTURE_FILE" | awk '{print $5}')
    echo "   ✅ 抓包文件大小: $PCAP_SIZE"

    # 检查数据包数量
    if command -v capinfos &> /dev/null; then
        PACKET_COUNT=$(capinfos "$CAPTURE_FILE" 2>/dev/null | grep "Number of packets" | awk '{print $4}')
        echo "   📦 数据包数量: $PACKET_COUNT"

        if [ "$PACKET_COUNT" -gt 0 ]; then
            echo "   🎉 成功捕获到数据包！"

            # 显示前几个数据包的基本信息
            echo ""
            echo "   📋 前5个数据包预览:"
            tshark -r "$CAPTURE_FILE" -c 5 -T fields \
                -e frame.number -e ip.src -e ip.dst -e udp.srcport -e udp.dstport -e frame.len \
                2>/dev/null | sed 's/^/      /' || echo "      无法解析数据包"
        else
            echo "   ⚠️  抓包文件为空"
        fi
    fi
else
    echo "   ❌ 抓包文件未生成"
fi

if [ -f "$SSLKEYLOGFILE" ]; then
    KEYLOG_SIZE=$(ls -lh "$SSLKEYLOGFILE" | awk '{print $5}')
    KEYLOG_LINES=$(wc -l < "$SSLKEYLOGFILE")
    echo "   ✅ 密钥文件大小: $KEYLOG_SIZE"
    echo "   🔑 密钥文件行数: $KEYLOG_LINES"

    if [ $KEYLOG_LINES -gt 0 ]; then
        echo ""
        echo "   🔍 密钥类型统计:"
        grep -o "^[A-Z_]*" "$SSLKEYLOGFILE" | sort | uniq -c | sed 's/^/      /'
    fi
else
    echo "   ❌ 密钥文件未生成"
fi

echo ""
echo "10. QPACK 动态表扩展性验证..."
if [ -f "$LOG_FILE" ]; then
    echo "    📊 头部数量增长序列:"
    grep "🔧 QPACK TEST.*will add.*headers" "$LOG_FILE" | sed 's/^/       /'

    echo ""
    echo "    🎯 动态表索引使用情况:"
    grep "✅ DYNAMIC TABLE HIT" "$LOG_FILE" | head -10 | sed 's/^/       /'

    echo ""
    echo "    📈 压缩效果统计:"
    grep -E "Header block size|Encoder stream" "$LOG_FILE" | sed 's/^/       /'

    echo ""
    echo "    🚀 最终压缩验证（200个头部）:"
    grep -A5 -B5 "Request #12\|200.*headers" "$LOG_FILE" | sed 's/^/       /'
else
    echo "    ❌ 测试日志文件未找到: $LOG_FILE"
fi

echo ""
echo "=== QPACK 动态表扩展性测试完成 ==="
echo "📁 生成的文件:"
echo "   抓包文件: $CAPTURE_FILE"
echo "   密钥文件: $SSLKEYLOGFILE"
echo "   测试日志: $LOG_FILE"

# 如果捕获到数据包，提供解密分析命令
if [ -f "$CAPTURE_FILE" ]; then
    PACKET_COUNT=$(capinfos "$CAPTURE_FILE" 2>/dev/null | grep "Number of packets" | awk '{print $4}')
    if [ "$PACKET_COUNT" -gt 0 ]; then
        echo ""
        echo "🔍 QPACK 扩展性分析命令:"
        echo "   # 查看 QUIC 数据包:"
        echo "   tshark -r \"$CAPTURE_FILE\" \\"
        echo "     -o tls.keylog_file:\"$SSLKEYLOGFILE\" \\"
        echo "     -Y \"quic\" -c 10"
        echo ""
        echo "   # 查看解密后的 HTTP/3 QPACK 数据:"
        echo "   tshark -r \"$CAPTURE_FILE\" \\"
        echo "     -o tls.keylog_file:\"$SSLKEYLOGFILE\" \\"
        echo "     -Y \"http3\" -V"
        echo ""
        echo "   # 分析 QPACK 动态表使用:"
        echo "   tshark -r \"$CAPTURE_FILE\" \\"
        echo "     -o tls.keylog_file:\"$SSLKEYLOGFILE\" \\"
        echo "     -Y \"http3.qpack\" -T fields -e http3.qpack.header.name -e http3.qpack.header.value"
    fi
fi
