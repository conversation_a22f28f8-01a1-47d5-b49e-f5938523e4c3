=== QPACK 抓包分析报告 ===
生成时间: Tue Jun 24 04:29:12 PM CST 2025
抓包文件: /home/<USER>/ngtcp2_qpack/qpack_test.pcap
密钥文件: /home/<USER>/ngtcp2_qpack/quic_keylog_file

1. 抓包文件基本信息:
File name:           /home/<USER>/ngtcp2_qpack/qpack_test.pcap
File type:           Wireshark/tcpdump/... - pcap
File encapsulation:  Linux cooked-mode capture v2
File timestamp precision:  microseconds (6)
Packet size limit:   file hdr: 262144 bytes
Number of packets:   0
File size:           24 bytes
Data size:           0 bytes
Capture duration:    n/a
First packet time:   n/a
Last packet time:    n/a
Data byte rate:      0 bytes/s
Data bit rate:       0 bits/s
Average packet size: 0.00 bytes
Average packet rate: 0 packets/s
SHA256:              e3f42e2687636327d7f18c9635173252505b4a838fa6829a755bab06c9c69749
RIPEMD160:           706f0722756f4156396c13c877532e22a5f9b144
SHA1:                1a994738a1e3057f1e377d1ebafb7ee856397b69
Strict time order:   True
Number of interfaces in file: 1
Interface #0 info:
                     Encapsulation = Linux cooked-mode capture v2 (210 - linux-sll2)
                     Capture length = 262144
                     Time precision = microseconds (6)
                     Time ticks per second = 1000000
                     Number of stat entries = 0
                     Number of packets = 0

2. QUIC 连接信息:
无法解析 QUIC 数据

3. HTTP/3 流量信息:
无法解析 HTTP/3 数据

4. QPACK 编码器/解码器流:

5. HTTP/3 SETTINGS 帧:
