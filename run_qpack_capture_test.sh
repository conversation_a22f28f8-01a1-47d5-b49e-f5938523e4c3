#!/bin/bash

echo "=== QPACK 抓包测试开始 ==="

# 设置环境变量
export SSLKEYLOGFILE=/home/<USER>/ngtcp2_qpack/quic_keylog_file

# 清理之前的文件
rm -f /home/<USER>/ngtcp2_qpack/qpack_test.pcap
rm -f /home/<USER>/ngtcp2_qpack/quic_keylog_file

echo "1. 启动后台抓包..."
sudo tcpdump -i any -w /home/<USER>/ngtcp2_qpack/qpack_test.pcap \
    host aliyun.hawks.top &
TCPDUMP_PID=$!

echo "   抓包进程 PID: $TCPDUMP_PID"

echo "2. 等待抓包启动..."
sleep 3

echo "3. 运行 QPACK 测试..."
cd /home/<USER>/ngtcp2_qpack/ngtcp2/examples
./wsslclient -n 3 aliyun.hawks.top 443 http://aliyun.hawks.top/ \
    > qpack_capture_test.log 2>&1

echo "4. 停止抓包..."
sudo kill $TCPDUMP_PID 2>/dev/null
sleep 2

echo "5. 验证生成的文件..."
echo "   抓包文件大小: $(ls -lh /home/<USER>/ngtcp2_qpack/qpack_test.pcap 2>/dev/null | awk '{print $5}' || echo '文件不存在')"
echo "   密钥文件大小: $(ls -lh /home/<USER>/ngtcp2_qpack/quic_keylog_file 2>/dev/null | awk '{print $5}' || echo '文件不存在')"

if [ -f /home/<USER>/ngtcp2_qpack/quic_keylog_file ]; then
    echo "   密钥文件行数: $(wc -l < /home/<USER>/ngtcp2_qpack/quic_keylog_file)"
    echo "   密钥文件前3行:"
    head -3 /home/<USER>/ngtcp2_qpack/quic_keylog_file
fi

echo "=== QPACK 抓包测试完成 ==="
echo "抓包文件: /home/<USER>/ngtcp2_qpack/qpack_test.pcap"
echo "密钥文件: /home/<USER>/ngtcp2_qpack/quic_keylog_file"
echo "测试日志: /home/<USER>/ngtcp2_qpack/ngtcp2/examples/qpack_capture_test.log"
