# QPACK 优化总结

## 概述

本文档总结了对 nghttp3 库中 QPACK 实现的优化工作。这些优化旨在提高 HTTP/3 头部压缩的效率，特别是对于带有 `NGHTTP3_NV_FLAG_TRY_INDEX` 标志的头部。

## 优化内容

### 1. 改进的索引决策逻辑

**文件**: `lib/nghttp3_qpack.c`，函数 `qpack_encoder_decide_indexing_mode`

#### 1.1 授权头部优化
- **原始行为**: 授权头部永远不被索引（安全考虑）
- **优化后**: 如果设置了 `TRY_INDEX` 标志，允许索引授权头部
- **代码位置**: 第 1322-1332 行

#### 1.2 Cookie 头部优化
- **原始行为**: 只有长度 >= 20 字节的 cookie 才被索引
- **优化后**: 
  - 长度阈值降低到 10 字节
  - 带有 `TRY_INDEX` 标志的短 cookie 也可以被索引
- **代码位置**: 第 1333-1345 行

#### 1.3 自定义头部优化
- **原始行为**: 自定义头部（token >= 1000）需要 `TRY_INDEX` 才能被索引
- **优化后**: 长值（>= 50 字节）的自定义头部即使没有 `TRY_INDEX` 也会被索引
- **代码位置**: 第 1367-1382 行

#### 1.4 容量检查优化
- **原始行为**: 所有头部使用 75% 容量阈值
- **优化后**: 带 `TRY_INDEX` 标志的头部使用 90% 容量阈值
- **代码位置**: 第 1388-1401 行

### 2. 动态表使用统计

**文件**: `lib/nghttp3_qpack.h`，结构体 `nghttp3_qpack_encoder`

#### 2.1 新增统计字段
```c
struct {
    uint64_t total_headers_processed;     /* 处理的头部总数 */
    uint64_t headers_indexed;             /* 被索引的头部数 */
    uint64_t headers_literal;             /* 使用字面量的头部数 */
    uint64_t dynamic_table_hits;          /* 动态表命中次数 */
    uint64_t static_table_hits;           /* 静态表命中次数 */
    uint64_t try_index_headers;           /* 带有 TRY_INDEX 标志的头部数 */
    uint64_t try_index_indexed;           /* 带有 TRY_INDEX 且被索引的头部数 */
    size_t max_dynamic_table_size;        /* 动态表达到的最大大小 */
    size_t total_bytes_saved;             /* 通过压缩节省的字节数估计 */
} stats;
```

#### 2.2 统计收集点
- 头部处理开始时更新总数
- 静态表命中时更新命中数和节省字节数
- 动态表命中时更新命中数和节省字节数
- 添加到动态表时更新索引数和最大大小
- 使用字面量时更新字面量数

#### 2.3 统计报告
- 每处理 10 个头部输出一次统计报告
- 包含索引率、命中率、压缩效果等关键指标

### 3. 详细调试输出

#### 3.1 索引决策过程
- 每个头部的索引决策详细日志
- 包含头部名称、值、标志和决策原因

#### 3.2 动态表操作
- 动态表添加、查找、命中的详细日志
- 动态表大小和容量使用情况

#### 3.3 压缩效果监控
- 实时的压缩统计信息
- 节省字节数的估计

## 测试验证

### 测试文件修改
- **文件**: `tests/nghttp3_qpack_test.c`
- **函数**: `test_nghttp3_qpack_encoder_encode_try_encode`
- **修改**: 调整期望的动态表条目数以反映优化效果

### 测试结果
- 原始实现：第一次编码 3 个动态表条目，带 TRY_INDEX 后 5 个条目
- 优化后：第一次编码 4 个动态表条目，带 TRY_INDEX 后 7 个条目
- **提升**: 索引率显著提高，更多头部被有效压缩

## 性能影响

### 正面影响
1. **更高的压缩率**: 更多头部被索引，减少传输字节数
2. **更好的缓存利用**: 动态表使用更充分
3. **智能索引**: 根据 TRY_INDEX 标志进行智能决策
4. **详细监控**: 统计信息帮助优化应用层使用

### 潜在考虑
1. **内存使用**: 更多索引可能增加动态表内存使用
2. **CPU 开销**: 额外的统计收集有轻微 CPU 开销
3. **安全性**: 授权头部索引需要应用层谨慎使用 TRY_INDEX

## 使用建议

### 应用层优化
1. 对重复的长头部使用 `NGHTTP3_NV_FLAG_TRY_INDEX`
2. 对敏感信息（如授权令牌）谨慎使用 TRY_INDEX
3. 监控统计输出来优化头部使用策略

### 部署建议
1. 在测试环境中验证压缩效果
2. 监控动态表内存使用情况
3. 根据应用特点调整索引策略

## 文件清单

### 修改的文件
1. `lib/nghttp3_qpack.h` - 添加统计结构
2. `lib/nghttp3_qpack.c` - 优化索引决策和添加统计
3. `tests/nghttp3_qpack_test.c` - 调整测试期望

### 新增文件
1. `test_qpack_optimization.c` - 优化效果演示程序
2. `QPACK_OPTIMIZATION_SUMMARY.md` - 本文档

## 结论

这些优化显著提高了 nghttp3 QPACK 实现的压缩效率，特别是对于使用 TRY_INDEX 标志的应用。通过智能的索引决策和详细的统计监控，应用可以更好地利用 QPACK 压缩来减少 HTTP/3 传输开销。
