#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "lib/nghttp3_qpack.h"
#include "lib/nghttp3_mem.h"

// 基于 nghttp3/tests/nghttp3_qpack_test.c 中的 MAKE_NV 宏定义
// 文件路径：nghttp3/tests/nghttp3_qpack_test.c，第 29 行
#define MAKE_NV(NAME, VALUE)                                                   \
  {                                                                            \
    (uint8_t *)NAME, (uint8_t *)VALUE, sizeof(NAME) - 1, sizeof(VALUE) - 1,   \
        NGHTTP3_NV_FLAG_NONE                                                   \
  }

#define MAKE_NV_TRY_INDEX(NAME, VALUE)                                         \
  {                                                                            \
    (uint8_t *)NAME, (uint8_t *)VALUE, sizeof(NAME) - 1, sizeof(VALUE) - 1,   \
        NGHTTP3_NV_FLAG_TRY_INDEX                                              \
  }

int main() {
    printf("=== QPACK 动态表索引优化调试测试 ===\n\n");
    
    // 使用 nghttp3_mem_default() 获取默认内存分配器
    // 函数定义：nghttp3/lib/nghttp3_mem.c
    const nghttp3_mem *mem = nghttp3_mem_default();
    
    // 初始化 QPACK 编码器
    // 结构体定义：nghttp3/lib/nghttp3_qpack.h，第 197-220 行
    nghttp3_qpack_encoder enc;
    
    // 初始化编码器，容量设为 4096 字节
    // 函数声明：nghttp3/lib/nghttp3_qpack.h，第 464 行
    // 函数实现：nghttp3/lib/nghttp3_qpack.c，第 1006 行
    nghttp3_qpack_encoder_init(&enc, 4096, mem);
    
    // 设置最大阻塞流数量
    // 函数实现：nghttp3/lib/nghttp3_qpack.c，第 1020 行
    nghttp3_qpack_encoder_set_max_blocked_streams(&enc, 1);
    
    // 设置动态表最大容量
    // 函数实现：nghttp3/lib/nghttp3_qpack.c，第 1025 行
    nghttp3_qpack_encoder_set_max_dtable_capacity(&enc, 4096);
    
    // 初始化缓冲区
    // 结构体定义：nghttp3/lib/nghttp3_buf.h，第 35-42 行
    nghttp3_buf pbuf, rbuf, ebuf;
    nghttp3_buf_init(&pbuf);
    nghttp3_buf_init(&rbuf);
    nghttp3_buf_init(&ebuf);
    
    printf("1. 测试普通头部字段（无 TRY_INDEX 标志）\n");
    printf("----------------------------------------\n");
    
    // 创建普通头部字段数组
    // nghttp3_nv 结构体定义：nghttp3/lib/includes/nghttp3/nghttp3.h，第 1570-1580 行
    nghttp3_nv nva1[] = {
        MAKE_NV(":method", "GET"),
        MAKE_NV(":path", "/"),
        MAKE_NV(":authority", "example.com"),
        MAKE_NV("user-agent", "Mozilla/5.0 (Test Browser)"),
        MAKE_NV("cookie", "short_cookie=value"),  // 短 cookie，应该不被索引
    };
    
    // 调用 QPACK 编码器进行编码
    // 函数声明：nghttp3/lib/nghttp3_qpack.h，第 508 行
    // 函数实现：nghttp3/lib/nghttp3_qpack.c，第 1132 行
    int rv = nghttp3_qpack_encoder_encode(&enc, &pbuf, &rbuf, &ebuf, 0, nva1, 
                                          sizeof(nva1)/sizeof(nva1[0]));
    
    if (rv != 0) {
        printf("编码失败: %d\n", rv);
        return 1;
    }
    
    printf("\n2. 测试带有 TRY_INDEX 标志的头部字段\n");
    printf("----------------------------------------\n");
    
    // 重置缓冲区
    nghttp3_buf_reset(&pbuf);
    nghttp3_buf_reset(&rbuf);
    nghttp3_buf_reset(&ebuf);
    
    // 创建带有 TRY_INDEX 标志的头部字段数组
    nghttp3_nv nva2[] = {
        MAKE_NV_TRY_INDEX(":method", "GET"),
        MAKE_NV_TRY_INDEX(":path", "/api/v1/data"),
        MAKE_NV_TRY_INDEX(":authority", "api.example.com"),
        MAKE_NV_TRY_INDEX("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"),
        MAKE_NV_TRY_INDEX("cookie", "session_id=abc123def456ghi789jkl012mno345pqr678stu901vwx234yz567890abcdef123456789"),  // 长 cookie
        MAKE_NV_TRY_INDEX("authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"),  // 长授权头
        MAKE_NV_TRY_INDEX("custom-header", "very-long-custom-header-value-that-should-be-indexed-due-to-try-index-flag"),
    };
    
    rv = nghttp3_qpack_encoder_encode(&enc, &pbuf, &rbuf, &ebuf, 4, nva2, 
                                      sizeof(nva2)/sizeof(nva2[0]));
    
    if (rv != 0) {
        printf("编码失败: %d\n", rv);
        return 1;
    }
    
    printf("\n3. 测试重复请求（验证动态表命中）\n");
    printf("----------------------------------------\n");
    
    // 重置缓冲区
    nghttp3_buf_reset(&pbuf);
    nghttp3_buf_reset(&rbuf);
    nghttp3_buf_reset(&ebuf);
    
    // 发送相同的头部字段，应该能够从动态表中找到
    rv = nghttp3_qpack_encoder_encode(&enc, &pbuf, &rbuf, &ebuf, 8, nva2, 
                                      sizeof(nva2)/sizeof(nva2[0]));
    
    if (rv != 0) {
        printf("编码失败: %d\n", rv);
        return 1;
    }
    
    printf("\n4. 动态表状态总结\n");
    printf("----------------------------------------\n");
    
    // 输出动态表的最终状态
    // nghttp3_qpack_context 结构体定义：nghttp3/lib/nghttp3_qpack.h，第 155-181 行
    // nghttp3_ringbuf 结构体定义：nghttp3/lib/nghttp3_ringbuf.h，第 37-50 行
    printf("动态表条目数: %zu\n", nghttp3_ringbuf_len(&enc.ctx.dtable));
    printf("动态表大小: %zu 字节\n", enc.ctx.dtable_size);
    printf("动态表容量: %zu 字节\n", enc.ctx.max_dtable_capacity);
    printf("下一个绝对索引: %lu\n", (unsigned long)enc.ctx.next_absidx);
    
    // 清理资源
    nghttp3_qpack_encoder_free(&enc);
    nghttp3_buf_free(&pbuf, mem);
    nghttp3_buf_free(&rbuf, mem);
    nghttp3_buf_free(&ebuf, mem);
    
    printf("\n=== 测试完成 ===\n");
    return 0;
}
