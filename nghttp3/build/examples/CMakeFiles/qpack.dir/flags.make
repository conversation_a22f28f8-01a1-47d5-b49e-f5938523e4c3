# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DHAVE_CONFIG_H

CXX_INCLUDES = -I/home/<USER>/ngtcp2_qpack/nghttp3/build -I/home/<USER>/ngtcp2_qpack/nghttp3/lib/includes -I/home/<USER>/ngtcp2_qpack/nghttp3/build/lib/includes

CXX_FLAGS = -O2 -g    -Wall -Wformat-security -Wno-noexcept-type -std=gnu++17

