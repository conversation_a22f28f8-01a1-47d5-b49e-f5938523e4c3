
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack.cc" "examples/CMakeFiles/qpack.dir/qpack.cc.o" "gcc" "examples/CMakeFiles/qpack.dir/qpack.cc.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack_decode.cc" "examples/CMakeFiles/qpack.dir/qpack_decode.cc.o" "gcc" "examples/CMakeFiles/qpack.dir/qpack_decode.cc.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack_encode.cc" "examples/CMakeFiles/qpack.dir/qpack_encode.cc.o" "gcc" "examples/CMakeFiles/qpack.dir/qpack_encode.cc.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/examples/util.cc" "examples/CMakeFiles/qpack.dir/util.cc.o" "gcc" "examples/CMakeFiles/qpack.dir/util.cc.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/ngtcp2_qpack/nghttp3/build/lib/CMakeFiles/nghttp3.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
