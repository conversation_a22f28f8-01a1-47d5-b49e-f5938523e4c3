# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ngtcp2_qpack/nghttp3

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ngtcp2_qpack/nghttp3/build

# Include any dependencies generated for this target.
include examples/CMakeFiles/qpack.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include examples/CMakeFiles/qpack.dir/compiler_depend.make

# Include the progress variables for this target.
include examples/CMakeFiles/qpack.dir/progress.make

# Include the compile flags for this target's objects.
include examples/CMakeFiles/qpack.dir/flags.make

examples/CMakeFiles/qpack.dir/qpack.cc.o: examples/CMakeFiles/qpack.dir/flags.make
examples/CMakeFiles/qpack.dir/qpack.cc.o: ../examples/qpack.cc
examples/CMakeFiles/qpack.dir/qpack.cc.o: examples/CMakeFiles/qpack.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object examples/CMakeFiles/qpack.dir/qpack.cc.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT examples/CMakeFiles/qpack.dir/qpack.cc.o -MF CMakeFiles/qpack.dir/qpack.cc.o.d -o CMakeFiles/qpack.dir/qpack.cc.o -c /home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack.cc

examples/CMakeFiles/qpack.dir/qpack.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/qpack.dir/qpack.cc.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack.cc > CMakeFiles/qpack.dir/qpack.cc.i

examples/CMakeFiles/qpack.dir/qpack.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/qpack.dir/qpack.cc.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack.cc -o CMakeFiles/qpack.dir/qpack.cc.s

examples/CMakeFiles/qpack.dir/qpack_encode.cc.o: examples/CMakeFiles/qpack.dir/flags.make
examples/CMakeFiles/qpack.dir/qpack_encode.cc.o: ../examples/qpack_encode.cc
examples/CMakeFiles/qpack.dir/qpack_encode.cc.o: examples/CMakeFiles/qpack.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object examples/CMakeFiles/qpack.dir/qpack_encode.cc.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT examples/CMakeFiles/qpack.dir/qpack_encode.cc.o -MF CMakeFiles/qpack.dir/qpack_encode.cc.o.d -o CMakeFiles/qpack.dir/qpack_encode.cc.o -c /home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack_encode.cc

examples/CMakeFiles/qpack.dir/qpack_encode.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/qpack.dir/qpack_encode.cc.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack_encode.cc > CMakeFiles/qpack.dir/qpack_encode.cc.i

examples/CMakeFiles/qpack.dir/qpack_encode.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/qpack.dir/qpack_encode.cc.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack_encode.cc -o CMakeFiles/qpack.dir/qpack_encode.cc.s

examples/CMakeFiles/qpack.dir/qpack_decode.cc.o: examples/CMakeFiles/qpack.dir/flags.make
examples/CMakeFiles/qpack.dir/qpack_decode.cc.o: ../examples/qpack_decode.cc
examples/CMakeFiles/qpack.dir/qpack_decode.cc.o: examples/CMakeFiles/qpack.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object examples/CMakeFiles/qpack.dir/qpack_decode.cc.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT examples/CMakeFiles/qpack.dir/qpack_decode.cc.o -MF CMakeFiles/qpack.dir/qpack_decode.cc.o.d -o CMakeFiles/qpack.dir/qpack_decode.cc.o -c /home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack_decode.cc

examples/CMakeFiles/qpack.dir/qpack_decode.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/qpack.dir/qpack_decode.cc.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack_decode.cc > CMakeFiles/qpack.dir/qpack_decode.cc.i

examples/CMakeFiles/qpack.dir/qpack_decode.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/qpack.dir/qpack_decode.cc.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/examples/qpack_decode.cc -o CMakeFiles/qpack.dir/qpack_decode.cc.s

examples/CMakeFiles/qpack.dir/util.cc.o: examples/CMakeFiles/qpack.dir/flags.make
examples/CMakeFiles/qpack.dir/util.cc.o: ../examples/util.cc
examples/CMakeFiles/qpack.dir/util.cc.o: examples/CMakeFiles/qpack.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object examples/CMakeFiles/qpack.dir/util.cc.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT examples/CMakeFiles/qpack.dir/util.cc.o -MF CMakeFiles/qpack.dir/util.cc.o.d -o CMakeFiles/qpack.dir/util.cc.o -c /home/<USER>/ngtcp2_qpack/nghttp3/examples/util.cc

examples/CMakeFiles/qpack.dir/util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/qpack.dir/util.cc.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/examples/util.cc > CMakeFiles/qpack.dir/util.cc.i

examples/CMakeFiles/qpack.dir/util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/qpack.dir/util.cc.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/examples/util.cc -o CMakeFiles/qpack.dir/util.cc.s

# Object files for target qpack
qpack_OBJECTS = \
"CMakeFiles/qpack.dir/qpack.cc.o" \
"CMakeFiles/qpack.dir/qpack_encode.cc.o" \
"CMakeFiles/qpack.dir/qpack_decode.cc.o" \
"CMakeFiles/qpack.dir/util.cc.o"

# External object files for target qpack
qpack_EXTERNAL_OBJECTS =

examples/qpack: examples/CMakeFiles/qpack.dir/qpack.cc.o
examples/qpack: examples/CMakeFiles/qpack.dir/qpack_encode.cc.o
examples/qpack: examples/CMakeFiles/qpack.dir/qpack_decode.cc.o
examples/qpack: examples/CMakeFiles/qpack.dir/util.cc.o
examples/qpack: examples/CMakeFiles/qpack.dir/build.make
examples/qpack: lib/libnghttp3.so.9.2.9
examples/qpack: examples/CMakeFiles/qpack.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX executable qpack"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/qpack.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
examples/CMakeFiles/qpack.dir/build: examples/qpack
.PHONY : examples/CMakeFiles/qpack.dir/build

examples/CMakeFiles/qpack.dir/clean:
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/examples && $(CMAKE_COMMAND) -P CMakeFiles/qpack.dir/cmake_clean.cmake
.PHONY : examples/CMakeFiles/qpack.dir/clean

examples/CMakeFiles/qpack.dir/depend:
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ngtcp2_qpack/nghttp3 /home/<USER>/ngtcp2_qpack/nghttp3/examples /home/<USER>/ngtcp2_qpack/nghttp3/build /home/<USER>/ngtcp2_qpack/nghttp3/build/examples /home/<USER>/ngtcp2_qpack/nghttp3/build/examples/CMakeFiles/qpack.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : examples/CMakeFiles/qpack.dir/depend

