
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/main.c" "tests/CMakeFiles/main.dir/main.c.o" "gcc" "tests/CMakeFiles/main.dir/main.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/munit/munit.c" "tests/CMakeFiles/main.dir/munit/munit.c.o" "gcc" "tests/CMakeFiles/main.dir/munit/munit.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/nghttp3_callbacks_test.c" "tests/CMakeFiles/main.dir/nghttp3_callbacks_test.c.o" "gcc" "tests/CMakeFiles/main.dir/nghttp3_callbacks_test.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/nghttp3_conn_test.c" "tests/CMakeFiles/main.dir/nghttp3_conn_test.c.o" "gcc" "tests/CMakeFiles/main.dir/nghttp3_conn_test.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/nghttp3_conv_test.c" "tests/CMakeFiles/main.dir/nghttp3_conv_test.c.o" "gcc" "tests/CMakeFiles/main.dir/nghttp3_conv_test.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/nghttp3_http_test.c" "tests/CMakeFiles/main.dir/nghttp3_http_test.c.o" "gcc" "tests/CMakeFiles/main.dir/nghttp3_http_test.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/nghttp3_qpack_test.c" "tests/CMakeFiles/main.dir/nghttp3_qpack_test.c.o" "gcc" "tests/CMakeFiles/main.dir/nghttp3_qpack_test.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/nghttp3_settings_test.c" "tests/CMakeFiles/main.dir/nghttp3_settings_test.c.o" "gcc" "tests/CMakeFiles/main.dir/nghttp3_settings_test.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/nghttp3_stream_test.c" "tests/CMakeFiles/main.dir/nghttp3_stream_test.c.o" "gcc" "tests/CMakeFiles/main.dir/nghttp3_stream_test.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/nghttp3_test_helper.c" "tests/CMakeFiles/main.dir/nghttp3_test_helper.c.o" "gcc" "tests/CMakeFiles/main.dir/nghttp3_test_helper.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/tests/nghttp3_tnode_test.c" "tests/CMakeFiles/main.dir/nghttp3_tnode_test.c.o" "gcc" "tests/CMakeFiles/main.dir/nghttp3_tnode_test.c.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/ngtcp2_qpack/nghttp3/build/lib/CMakeFiles/nghttp3_static.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
