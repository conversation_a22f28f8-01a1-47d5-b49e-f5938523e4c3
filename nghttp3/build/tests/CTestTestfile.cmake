# CMake generated Testfile for 
# Source directory: /home/<USER>/ngtcp2_qpack/nghttp3/tests
# Build directory: /home/<USER>/ngtcp2_qpack/nghttp3/build/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test([=[main]=] "main")
set_tests_properties([=[main]=] PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/ngtcp2_qpack/nghttp3/tests/CMakeLists.txt;55;add_test;/home/<USER>/ngtcp2_qpack/nghttp3/tests/CMakeLists.txt;0;")
