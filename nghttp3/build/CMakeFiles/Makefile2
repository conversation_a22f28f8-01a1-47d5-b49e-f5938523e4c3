# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ngtcp2_qpack/nghttp3

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ngtcp2_qpack/nghttp3/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: lib/all
all: tests/all
all: examples/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: lib/preinstall
preinstall: tests/preinstall
preinstall: examples/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/check.dir/clean
clean: lib/clean
clean: tests/clean
clean: examples/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory examples

# Recursive "all" directory target.
examples/all: examples/CMakeFiles/qpack.dir/all
.PHONY : examples/all

# Recursive "preinstall" directory target.
examples/preinstall:
.PHONY : examples/preinstall

# Recursive "clean" directory target.
examples/clean: examples/CMakeFiles/qpack.dir/clean
.PHONY : examples/clean

#=============================================================================
# Directory level rules for directory lib

# Recursive "all" directory target.
lib/all: lib/CMakeFiles/nghttp3.dir/all
lib/all: lib/CMakeFiles/nghttp3_static.dir/all
lib/all: lib/includes/all
.PHONY : lib/all

# Recursive "preinstall" directory target.
lib/preinstall: lib/includes/preinstall
.PHONY : lib/preinstall

# Recursive "clean" directory target.
lib/clean: lib/CMakeFiles/nghttp3.dir/clean
lib/clean: lib/CMakeFiles/nghttp3_static.dir/clean
lib/clean: lib/includes/clean
.PHONY : lib/clean

#=============================================================================
# Directory level rules for directory lib/includes

# Recursive "all" directory target.
lib/includes/all:
.PHONY : lib/includes/all

# Recursive "preinstall" directory target.
lib/includes/preinstall:
.PHONY : lib/includes/preinstall

# Recursive "clean" directory target.
lib/includes/clean:
.PHONY : lib/includes/clean

#=============================================================================
# Directory level rules for directory tests

# Recursive "all" directory target.
tests/all:
.PHONY : tests/all

# Recursive "preinstall" directory target.
tests/preinstall:
.PHONY : tests/preinstall

# Recursive "clean" directory target.
tests/clean: tests/CMakeFiles/main.dir/clean
.PHONY : tests/clean

#=============================================================================
# Target rules for target CMakeFiles/check.dir

# All Build rule for target.
CMakeFiles/check.dir/all: tests/CMakeFiles/main.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/check.dir/build.make CMakeFiles/check.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/check.dir/build.make CMakeFiles/check.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num= "Built target check"
.PHONY : CMakeFiles/check.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/check.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles 44
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/check.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles 0
.PHONY : CMakeFiles/check.dir/rule

# Convenience name for target.
check: CMakeFiles/check.dir/rule
.PHONY : check

# clean rule for target.
CMakeFiles/check.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/check.dir/build.make CMakeFiles/check.dir/clean
.PHONY : CMakeFiles/check.dir/clean

#=============================================================================
# Target rules for target lib/CMakeFiles/nghttp3.dir

# All Build rule for target.
lib/CMakeFiles/nghttp3.dir/all:
	$(MAKE) $(MAKESILENT) -f lib/CMakeFiles/nghttp3.dir/build.make lib/CMakeFiles/nghttp3.dir/depend
	$(MAKE) $(MAKESILENT) -f lib/CMakeFiles/nghttp3.dir/build.make lib/CMakeFiles/nghttp3.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44 "Built target nghttp3"
.PHONY : lib/CMakeFiles/nghttp3.dir/all

# Build rule for subdir invocation for target.
lib/CMakeFiles/nghttp3.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles 32
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/nghttp3.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles 0
.PHONY : lib/CMakeFiles/nghttp3.dir/rule

# Convenience name for target.
nghttp3: lib/CMakeFiles/nghttp3.dir/rule
.PHONY : nghttp3

# clean rule for target.
lib/CMakeFiles/nghttp3.dir/clean:
	$(MAKE) $(MAKESILENT) -f lib/CMakeFiles/nghttp3.dir/build.make lib/CMakeFiles/nghttp3.dir/clean
.PHONY : lib/CMakeFiles/nghttp3.dir/clean

#=============================================================================
# Target rules for target lib/CMakeFiles/nghttp3_static.dir

# All Build rule for target.
lib/CMakeFiles/nghttp3_static.dir/all:
	$(MAKE) $(MAKESILENT) -f lib/CMakeFiles/nghttp3_static.dir/build.make lib/CMakeFiles/nghttp3_static.dir/depend
	$(MAKE) $(MAKESILENT) -f lib/CMakeFiles/nghttp3_static.dir/build.make lib/CMakeFiles/nghttp3_static.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76 "Built target nghttp3_static"
.PHONY : lib/CMakeFiles/nghttp3_static.dir/all

# Build rule for subdir invocation for target.
lib/CMakeFiles/nghttp3_static.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles 32
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/nghttp3_static.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles 0
.PHONY : lib/CMakeFiles/nghttp3_static.dir/rule

# Convenience name for target.
nghttp3_static: lib/CMakeFiles/nghttp3_static.dir/rule
.PHONY : nghttp3_static

# clean rule for target.
lib/CMakeFiles/nghttp3_static.dir/clean:
	$(MAKE) $(MAKESILENT) -f lib/CMakeFiles/nghttp3_static.dir/build.make lib/CMakeFiles/nghttp3_static.dir/clean
.PHONY : lib/CMakeFiles/nghttp3_static.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/main.dir

# All Build rule for target.
tests/CMakeFiles/main.dir/all: lib/CMakeFiles/nghttp3_static.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/main.dir/build.make tests/CMakeFiles/main.dir/depend
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/main.dir/build.make tests/CMakeFiles/main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12 "Built target main"
.PHONY : tests/CMakeFiles/main.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles 44
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles 0
.PHONY : tests/CMakeFiles/main.dir/rule

# Convenience name for target.
main: tests/CMakeFiles/main.dir/rule
.PHONY : main

# clean rule for target.
tests/CMakeFiles/main.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/main.dir/build.make tests/CMakeFiles/main.dir/clean
.PHONY : tests/CMakeFiles/main.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/qpack.dir

# All Build rule for target.
examples/CMakeFiles/qpack.dir/all: lib/CMakeFiles/nghttp3.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/qpack.dir/build.make examples/CMakeFiles/qpack.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/qpack.dir/build.make examples/CMakeFiles/qpack.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=77,78,79,80,81 "Built target qpack"
.PHONY : examples/CMakeFiles/qpack.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/qpack.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/qpack.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/qpack.dir/rule

# Convenience name for target.
qpack: examples/CMakeFiles/qpack.dir/rule
.PHONY : qpack

# clean rule for target.
examples/CMakeFiles/qpack.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/qpack.dir/build.make examples/CMakeFiles/qpack.dir/clean
.PHONY : examples/CMakeFiles/qpack.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

