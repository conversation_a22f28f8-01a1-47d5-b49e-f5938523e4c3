# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "../CMakeOptions.txt"
  "CMakeFiles/3.22.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeSystem.cmake"
  "CMakeFiles/CheckTypeSize/SIZEOF_SSIZE_T.c"
  "../cmake/PickyWarningsC.cmake"
  "../cmake/PickyWarningsCXX.cmake"
  "../cmake/Version.cmake"
  "../cmakeconfig.h.in"
  "../examples/CMakeLists.txt"
  "../lib/CMakeLists.txt"
  "../lib/config.cmake.in"
  "../lib/includes/CMakeLists.txt"
  "../lib/includes/nghttp3/version.h.in"
  "../lib/libnghttp3.pc.in"
  "../tests/CMakeLists.txt"
  "/usr/share/cmake-3.22/Modules/BasicConfigVersion-SameMajorVersion.cmake.in"
  "/usr/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in"
  "/usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c"
  "/usr/share/cmake-3.22/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeConfigurableFile.in"
  "/usr/share/cmake-3.22/Modules/CMakeDependentOption.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakePackageConfigHelpers.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCXXCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/CheckIncludeFile.c.in"
  "/usr/share/cmake-3.22/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake-3.22/Modules/CheckIncludeFileCXX.cmake"
  "/usr/share/cmake-3.22/Modules/CheckSymbolExists.cmake"
  "/usr/share/cmake-3.22/Modules/CheckTypeSize.c.in"
  "/usr/share/cmake-3.22/Modules/CheckTypeSize.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.22/Modules/GNUInstallDirs.cmake"
  "/usr/share/cmake-3.22/Modules/Internal/CheckCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake"
  "/usr/share/cmake-3.22/Modules/WriteBasicConfigVersionFile.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.22.1/CMakeSystem.cmake"
  "CMakeFiles/3.22.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/CheckTypeSize/SIZEOF_SSIZE_T.c"
  "config.h"
  "lib/libnghttp3.pc"
  "lib/includes/nghttp3/version.h"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "lib/generated/nghttp3ConfigVersion.cmake"
  "lib/generated/nghttp3Config.cmake"
  "lib/CMakeFiles/CMakeDirectoryInformation.cmake"
  "lib/includes/CMakeFiles/CMakeDirectoryInformation.cmake"
  "tests/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/check.dir/DependInfo.cmake"
  "lib/CMakeFiles/nghttp3.dir/DependInfo.cmake"
  "lib/CMakeFiles/nghttp3_static.dir/DependInfo.cmake"
  "tests/CMakeFiles/main.dir/DependInfo.cmake"
  "examples/CMakeFiles/qpack.dir/DependInfo.cmake"
  )
