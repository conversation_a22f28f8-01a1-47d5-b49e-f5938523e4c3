lib/CMakeFiles/nghttp3_static.dir/nghttp3_http.c.o: \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_http.c \
 /usr/include/stdc-predef.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_http.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/build/config.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/includes/nghttp3/nghttp3.h \
 /usr/include/stdlib.h \
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
 /usr/include/features.h /usr/include/features-time64.h \
 /usr/include/x86_64-linux-gnu/bits/wordsize.h \
 /usr/include/x86_64-linux-gnu/bits/timesize.h \
 /usr/include/x86_64-linux-gnu/sys/cdefs.h \
 /usr/include/x86_64-linux-gnu/bits/long-double.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h \
 /usr/include/x86_64-linux-gnu/bits/waitflags.h \
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
 /usr/include/x86_64-linux-gnu/bits/floatn.h \
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
 /usr/include/x86_64-linux-gnu/sys/types.h \
 /usr/include/x86_64-linux-gnu/bits/types.h \
 /usr/include/x86_64-linux-gnu/bits/typesizes.h \
 /usr/include/x86_64-linux-gnu/bits/time64.h \
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h /usr/include/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endianness.h \
 /usr/include/x86_64-linux-gnu/bits/byteswap.h \
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
 /usr/include/x86_64-linux-gnu/sys/select.h \
 /usr/include/x86_64-linux-gnu/bits/select.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/x86_64-linux-gnu/bits/select2.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h /usr/include/alloca.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib.h /usr/include/inttypes.h \
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h /usr/include/stdint.h \
 /usr/include/x86_64-linux-gnu/bits/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/includes/nghttp3/version.h \
 /usr/include/string.h \
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/strings.h \
 /usr/include/x86_64-linux-gnu/bits/strings_fortified.h \
 /usr/include/x86_64-linux-gnu/bits/string_fortified.h \
 /usr/include/assert.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_stream.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_map.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_mem.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_tnode.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_pq.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_ringbuf.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_buf.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_frame.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_rcbuf.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_ksl.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_objalloc.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_balloc.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_opl.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_macro.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack_huffman.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_conv.h \
 /usr/include/arpa/inet.h /usr/include/netinet/in.h \
 /usr/include/x86_64-linux-gnu/sys/socket.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_iovec.h \
 /usr/include/x86_64-linux-gnu/bits/socket.h \
 /usr/include/x86_64-linux-gnu/bits/socket_type.h \
 /usr/include/x86_64-linux-gnu/bits/sockaddr.h \
 /usr/include/x86_64-linux-gnu/asm/socket.h \
 /usr/include/asm-generic/socket.h /usr/include/linux/posix_types.h \
 /usr/include/linux/stddef.h \
 /usr/include/x86_64-linux-gnu/asm/posix_types.h \
 /usr/include/x86_64-linux-gnu/asm/posix_types_64.h \
 /usr/include/asm-generic/posix_types.h \
 /usr/include/x86_64-linux-gnu/asm/bitsperlong.h \
 /usr/include/asm-generic/bitsperlong.h \
 /usr/include/x86_64-linux-gnu/asm/sockios.h \
 /usr/include/asm-generic/sockios.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_osockaddr.h \
 /usr/include/x86_64-linux-gnu/bits/socket2.h \
 /usr/include/x86_64-linux-gnu/bits/in.h /usr/include/byteswap.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_unreachable.h \
 /home/<USER>/ngtcp2_qpack/nghttp3/lib/sfparse/sfparse.h
