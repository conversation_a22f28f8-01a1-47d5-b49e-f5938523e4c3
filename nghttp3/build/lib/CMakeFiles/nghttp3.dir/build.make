# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ngtcp2_qpack/nghttp3

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ngtcp2_qpack/nghttp3/build

# Include any dependencies generated for this target.
include lib/CMakeFiles/nghttp3.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include lib/CMakeFiles/nghttp3.dir/compiler_depend.make

# Include the progress variables for this target.
include lib/CMakeFiles/nghttp3.dir/progress.make

# Include the compile flags for this target's objects.
include lib/CMakeFiles/nghttp3.dir/flags.make

lib/CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o: ../lib/nghttp3_rcbuf.c
lib/CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_rcbuf.c

lib/CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_rcbuf.c > CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_rcbuf.c -o CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_mem.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_mem.c.o: ../lib/nghttp3_mem.c
lib/CMakeFiles/nghttp3.dir/nghttp3_mem.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_mem.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_mem.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_mem.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_mem.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_mem.c

lib/CMakeFiles/nghttp3.dir/nghttp3_mem.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_mem.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_mem.c > CMakeFiles/nghttp3.dir/nghttp3_mem.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_mem.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_mem.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_mem.c -o CMakeFiles/nghttp3.dir/nghttp3_mem.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_str.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_str.c.o: ../lib/nghttp3_str.c
lib/CMakeFiles/nghttp3.dir/nghttp3_str.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_str.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_str.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_str.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_str.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_str.c

lib/CMakeFiles/nghttp3.dir/nghttp3_str.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_str.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_str.c > CMakeFiles/nghttp3.dir/nghttp3_str.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_str.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_str.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_str.c -o CMakeFiles/nghttp3.dir/nghttp3_str.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_conv.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_conv.c.o: ../lib/nghttp3_conv.c
lib/CMakeFiles/nghttp3.dir/nghttp3_conv.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_conv.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_conv.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_conv.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_conv.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_conv.c

lib/CMakeFiles/nghttp3.dir/nghttp3_conv.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_conv.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_conv.c > CMakeFiles/nghttp3.dir/nghttp3_conv.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_conv.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_conv.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_conv.c -o CMakeFiles/nghttp3.dir/nghttp3_conv.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_buf.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_buf.c.o: ../lib/nghttp3_buf.c
lib/CMakeFiles/nghttp3.dir/nghttp3_buf.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_buf.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_buf.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_buf.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_buf.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_buf.c

lib/CMakeFiles/nghttp3.dir/nghttp3_buf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_buf.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_buf.c > CMakeFiles/nghttp3.dir/nghttp3_buf.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_buf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_buf.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_buf.c -o CMakeFiles/nghttp3.dir/nghttp3_buf.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o: ../lib/nghttp3_ringbuf.c
lib/CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_ringbuf.c

lib/CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_ringbuf.c > CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_ringbuf.c -o CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_pq.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_pq.c.o: ../lib/nghttp3_pq.c
lib/CMakeFiles/nghttp3.dir/nghttp3_pq.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_pq.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_pq.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_pq.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_pq.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_pq.c

lib/CMakeFiles/nghttp3.dir/nghttp3_pq.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_pq.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_pq.c > CMakeFiles/nghttp3.dir/nghttp3_pq.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_pq.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_pq.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_pq.c -o CMakeFiles/nghttp3.dir/nghttp3_pq.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_map.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_map.c.o: ../lib/nghttp3_map.c
lib/CMakeFiles/nghttp3.dir/nghttp3_map.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_map.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_map.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_map.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_map.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_map.c

lib/CMakeFiles/nghttp3.dir/nghttp3_map.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_map.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_map.c > CMakeFiles/nghttp3.dir/nghttp3_map.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_map.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_map.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_map.c -o CMakeFiles/nghttp3.dir/nghttp3_map.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o: ../lib/nghttp3_ksl.c
lib/CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_ksl.c

lib/CMakeFiles/nghttp3.dir/nghttp3_ksl.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_ksl.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_ksl.c > CMakeFiles/nghttp3.dir/nghttp3_ksl.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_ksl.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_ksl.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_ksl.c -o CMakeFiles/nghttp3.dir/nghttp3_ksl.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o: ../lib/nghttp3_qpack.c
lib/CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack.c

lib/CMakeFiles/nghttp3.dir/nghttp3_qpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_qpack.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack.c > CMakeFiles/nghttp3.dir/nghttp3_qpack.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_qpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_qpack.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack.c -o CMakeFiles/nghttp3.dir/nghttp3_qpack.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o: ../lib/nghttp3_qpack_huffman.c
lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack_huffman.c

lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack_huffman.c > CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack_huffman.c -o CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o: ../lib/nghttp3_qpack_huffman_data.c
lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack_huffman_data.c

lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack_huffman_data.c > CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack_huffman_data.c -o CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_err.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_err.c.o: ../lib/nghttp3_err.c
lib/CMakeFiles/nghttp3.dir/nghttp3_err.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_err.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_err.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_err.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_err.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_err.c

lib/CMakeFiles/nghttp3.dir/nghttp3_err.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_err.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_err.c > CMakeFiles/nghttp3.dir/nghttp3_err.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_err.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_err.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_err.c -o CMakeFiles/nghttp3.dir/nghttp3_err.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_debug.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_debug.c.o: ../lib/nghttp3_debug.c
lib/CMakeFiles/nghttp3.dir/nghttp3_debug.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_debug.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_debug.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_debug.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_debug.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_debug.c

lib/CMakeFiles/nghttp3.dir/nghttp3_debug.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_debug.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_debug.c > CMakeFiles/nghttp3.dir/nghttp3_debug.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_debug.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_debug.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_debug.c -o CMakeFiles/nghttp3.dir/nghttp3_debug.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_conn.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_conn.c.o: ../lib/nghttp3_conn.c
lib/CMakeFiles/nghttp3.dir/nghttp3_conn.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_conn.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_conn.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_conn.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_conn.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_conn.c

lib/CMakeFiles/nghttp3.dir/nghttp3_conn.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_conn.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_conn.c > CMakeFiles/nghttp3.dir/nghttp3_conn.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_conn.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_conn.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_conn.c -o CMakeFiles/nghttp3.dir/nghttp3_conn.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_stream.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_stream.c.o: ../lib/nghttp3_stream.c
lib/CMakeFiles/nghttp3.dir/nghttp3_stream.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_stream.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_stream.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_stream.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_stream.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_stream.c

lib/CMakeFiles/nghttp3.dir/nghttp3_stream.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_stream.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_stream.c > CMakeFiles/nghttp3.dir/nghttp3_stream.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_stream.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_stream.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_stream.c -o CMakeFiles/nghttp3.dir/nghttp3_stream.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_frame.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_frame.c.o: ../lib/nghttp3_frame.c
lib/CMakeFiles/nghttp3.dir/nghttp3_frame.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_frame.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_frame.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_frame.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_frame.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_frame.c

lib/CMakeFiles/nghttp3.dir/nghttp3_frame.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_frame.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_frame.c > CMakeFiles/nghttp3.dir/nghttp3_frame.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_frame.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_frame.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_frame.c -o CMakeFiles/nghttp3.dir/nghttp3_frame.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o: ../lib/nghttp3_tnode.c
lib/CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_tnode.c

lib/CMakeFiles/nghttp3.dir/nghttp3_tnode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_tnode.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_tnode.c > CMakeFiles/nghttp3.dir/nghttp3_tnode.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_tnode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_tnode.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_tnode.c -o CMakeFiles/nghttp3.dir/nghttp3_tnode.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_vec.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_vec.c.o: ../lib/nghttp3_vec.c
lib/CMakeFiles/nghttp3.dir/nghttp3_vec.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_vec.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_vec.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_vec.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_vec.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_vec.c

lib/CMakeFiles/nghttp3.dir/nghttp3_vec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_vec.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_vec.c > CMakeFiles/nghttp3.dir/nghttp3_vec.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_vec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_vec.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_vec.c -o CMakeFiles/nghttp3.dir/nghttp3_vec.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o: ../lib/nghttp3_gaptr.c
lib/CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_gaptr.c

lib/CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_gaptr.c > CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_gaptr.c -o CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o: ../lib/nghttp3_idtr.c
lib/CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_idtr.c

lib/CMakeFiles/nghttp3.dir/nghttp3_idtr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_idtr.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_idtr.c > CMakeFiles/nghttp3.dir/nghttp3_idtr.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_idtr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_idtr.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_idtr.c -o CMakeFiles/nghttp3.dir/nghttp3_idtr.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_range.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_range.c.o: ../lib/nghttp3_range.c
lib/CMakeFiles/nghttp3.dir/nghttp3_range.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_range.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_range.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_range.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_range.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_range.c

lib/CMakeFiles/nghttp3.dir/nghttp3_range.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_range.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_range.c > CMakeFiles/nghttp3.dir/nghttp3_range.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_range.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_range.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_range.c -o CMakeFiles/nghttp3.dir/nghttp3_range.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_http.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_http.c.o: ../lib/nghttp3_http.c
lib/CMakeFiles/nghttp3.dir/nghttp3_http.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_http.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_http.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_http.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_http.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_http.c

lib/CMakeFiles/nghttp3.dir/nghttp3_http.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_http.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_http.c > CMakeFiles/nghttp3.dir/nghttp3_http.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_http.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_http.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_http.c -o CMakeFiles/nghttp3.dir/nghttp3_http.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_version.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_version.c.o: ../lib/nghttp3_version.c
lib/CMakeFiles/nghttp3.dir/nghttp3_version.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_version.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_version.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_version.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_version.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_version.c

lib/CMakeFiles/nghttp3.dir/nghttp3_version.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_version.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_version.c > CMakeFiles/nghttp3.dir/nghttp3_version.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_version.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_version.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_version.c -o CMakeFiles/nghttp3.dir/nghttp3_version.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o: ../lib/nghttp3_balloc.c
lib/CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_balloc.c

lib/CMakeFiles/nghttp3.dir/nghttp3_balloc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_balloc.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_balloc.c > CMakeFiles/nghttp3.dir/nghttp3_balloc.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_balloc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_balloc.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_balloc.c -o CMakeFiles/nghttp3.dir/nghttp3_balloc.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_opl.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_opl.c.o: ../lib/nghttp3_opl.c
lib/CMakeFiles/nghttp3.dir/nghttp3_opl.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_opl.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_opl.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_opl.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_opl.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_opl.c

lib/CMakeFiles/nghttp3.dir/nghttp3_opl.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_opl.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_opl.c > CMakeFiles/nghttp3.dir/nghttp3_opl.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_opl.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_opl.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_opl.c -o CMakeFiles/nghttp3.dir/nghttp3_opl.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o: ../lib/nghttp3_objalloc.c
lib/CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_objalloc.c

lib/CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_objalloc.c > CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_objalloc.c -o CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o: ../lib/nghttp3_unreachable.c
lib/CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_unreachable.c

lib/CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_unreachable.c > CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_unreachable.c -o CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_settings.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_settings.c.o: ../lib/nghttp3_settings.c
lib/CMakeFiles/nghttp3.dir/nghttp3_settings.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_settings.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_settings.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_settings.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_settings.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_settings.c

lib/CMakeFiles/nghttp3.dir/nghttp3_settings.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_settings.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_settings.c > CMakeFiles/nghttp3.dir/nghttp3_settings.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_settings.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_settings.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_settings.c -o CMakeFiles/nghttp3.dir/nghttp3_settings.c.s

lib/CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o: ../lib/nghttp3_callbacks.c
lib/CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object lib/CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o -MF CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o.d -o CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_callbacks.c

lib/CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_callbacks.c > CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.i

lib/CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_callbacks.c -o CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.s

lib/CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o: lib/CMakeFiles/nghttp3.dir/flags.make
lib/CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o: ../lib/sfparse/sfparse.c
lib/CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o: lib/CMakeFiles/nghttp3.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object lib/CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT lib/CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o -MF CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o.d -o CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o -c /home/<USER>/ngtcp2_qpack/nghttp3/lib/sfparse/sfparse.c

lib/CMakeFiles/nghttp3.dir/sfparse/sfparse.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nghttp3.dir/sfparse/sfparse.c.i"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/ngtcp2_qpack/nghttp3/lib/sfparse/sfparse.c > CMakeFiles/nghttp3.dir/sfparse/sfparse.c.i

lib/CMakeFiles/nghttp3.dir/sfparse/sfparse.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nghttp3.dir/sfparse/sfparse.c.s"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/ngtcp2_qpack/nghttp3/lib/sfparse/sfparse.c -o CMakeFiles/nghttp3.dir/sfparse/sfparse.c.s

# Object files for target nghttp3
nghttp3_OBJECTS = \
"CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_mem.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_str.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_conv.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_buf.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_pq.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_map.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_err.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_debug.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_conn.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_stream.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_frame.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_vec.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_range.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_http.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_version.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_opl.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_settings.c.o" \
"CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o" \
"CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o"

# External object files for target nghttp3
nghttp3_EXTERNAL_OBJECTS =

lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_mem.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_str.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_conv.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_buf.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_pq.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_map.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_err.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_debug.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_conn.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_stream.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_frame.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_vec.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_range.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_http.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_version.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_opl.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_settings.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/build.make
lib/libnghttp3.so.9.2.9: lib/CMakeFiles/nghttp3.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/ngtcp2_qpack/nghttp3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Linking C shared library libnghttp3.so"
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/nghttp3.dir/link.txt --verbose=$(VERBOSE)
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && $(CMAKE_COMMAND) -E cmake_symlink_library libnghttp3.so.9.2.9 libnghttp3.so.9 libnghttp3.so

lib/libnghttp3.so.9: lib/libnghttp3.so.9.2.9
	@$(CMAKE_COMMAND) -E touch_nocreate lib/libnghttp3.so.9

lib/libnghttp3.so: lib/libnghttp3.so.9.2.9
	@$(CMAKE_COMMAND) -E touch_nocreate lib/libnghttp3.so

# Rule to build all files generated by this target.
lib/CMakeFiles/nghttp3.dir/build: lib/libnghttp3.so
.PHONY : lib/CMakeFiles/nghttp3.dir/build

lib/CMakeFiles/nghttp3.dir/clean:
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build/lib && $(CMAKE_COMMAND) -P CMakeFiles/nghttp3.dir/cmake_clean.cmake
.PHONY : lib/CMakeFiles/nghttp3.dir/clean

lib/CMakeFiles/nghttp3.dir/depend:
	cd /home/<USER>/ngtcp2_qpack/nghttp3/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ngtcp2_qpack/nghttp3 /home/<USER>/ngtcp2_qpack/nghttp3/lib /home/<USER>/ngtcp2_qpack/nghttp3/build /home/<USER>/ngtcp2_qpack/nghttp3/build/lib /home/<USER>/ngtcp2_qpack/nghttp3/build/lib/CMakeFiles/nghttp3.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : lib/CMakeFiles/nghttp3.dir/depend

