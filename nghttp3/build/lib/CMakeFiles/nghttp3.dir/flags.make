# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile C with /usr/bin/cc
C_DEFINES = -DBUILDING_NGHTTP3 -DHAVE_CONFIG_H -Dnghttp3_EXPORTS

C_INCLUDES = -I/home/<USER>/ngtcp2_qpack/nghttp3/build -I/home/<USER>/ngtcp2_qpack/nghttp3/lib/includes -I/home/<USER>/ngtcp2_qpack/nghttp3/build/lib/includes

C_FLAGS = -O2 -g  -fPIC -fvisibility=hidden   -W -Wall -Wconversion -Winline -Wmissing-declarations -Wmissing-prototypes -Wnested-externs -Wpointer-arith -Wshadow -Wundef -Wwrite-strings -Waddress -Wattributes -Wcast-align -Wdeclaration-after-statement -Wdiv-by-zero -Wempty-body -Wendif-labels -Wfloat-equal -Wformat-nonliteral -Wformat-security -Wmissing-field-initializers -Wmissing-noreturn -Wno-format-nonliteral -Wredundant-decls -Wsign-conversion -Wstrict-prototypes -Wunreachable-code -Wunused-macros -Wunused-parameter -Wvla -Wclobbered -Wduplicated-branches -Wpragmas

