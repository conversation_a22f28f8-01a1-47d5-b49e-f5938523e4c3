
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_balloc.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_balloc.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_buf.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_buf.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_buf.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_callbacks.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_callbacks.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_conn.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_conn.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_conn.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_conv.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_conv.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_conv.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_debug.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_debug.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_debug.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_err.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_err.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_err.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_frame.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_frame.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_frame.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_gaptr.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_gaptr.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_http.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_http.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_http.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_idtr.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_idtr.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_ksl.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_ksl.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_map.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_map.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_map.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_mem.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_mem.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_mem.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_objalloc.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_objalloc.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_opl.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_opl.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_opl.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_pq.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_pq.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_pq.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_qpack.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack_huffman.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_qpack_huffman_data.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_qpack_huffman_data.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_range.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_range.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_range.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_rcbuf.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_rcbuf.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_ringbuf.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_ringbuf.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_settings.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_settings.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_settings.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_str.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_str.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_str.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_stream.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_stream.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_stream.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_tnode.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_tnode.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_unreachable.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_unreachable.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_vec.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_vec.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_vec.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/nghttp3_version.c" "lib/CMakeFiles/nghttp3.dir/nghttp3_version.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/nghttp3_version.c.o.d"
  "/home/<USER>/ngtcp2_qpack/nghttp3/lib/sfparse/sfparse.c" "lib/CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o" "gcc" "lib/CMakeFiles/nghttp3.dir/sfparse/sfparse.c.o.d"
  )

# Pairs of files generated by the same build rule.
set(CMAKE_MULTIPLE_OUTPUT_PAIRS
  "/home/<USER>/ngtcp2_qpack/nghttp3/build/lib/libnghttp3.so" "/home/<USER>/ngtcp2_qpack/nghttp3/build/lib/libnghttp3.so.9.2.9"
  "/home/<USER>/ngtcp2_qpack/nghttp3/build/lib/libnghttp3.so.9" "/home/<USER>/ngtcp2_qpack/nghttp3/build/lib/libnghttp3.so.9.2.9"
  )


# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
