#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>


// 用于分析实际网络传输中的 QPACK 编码数据

void print_hex_dump(const uint8_t *data, size_t len, const char *prefix) {
    printf("%s (%zu bytes):\n", prefix, len);
    for (size_t i = 0; i < len; i++) {
        if (i % 16 == 0) printf("  %04zx: ", i);
        printf("%02x ", data[i]);
        if (i % 16 == 15 || i == len - 1) printf("\n");
    }
    printf("\n");
}

// 分析 QPACK 编码的头部块
void analyze_qpack_header_block(const uint8_t *data, size_t len, int request_num) {
    printf("=== QPACK Wire Analysis: Request #%d ===\n", request_num);
    print_hex_dump(data, len, "Raw QPACK encoded data");
    
    // 简单的 QPACK 解析
    size_t pos = 0;
    int field_count = 0;
    
    while (pos < len) {
        uint8_t byte = data[pos];
        
        if (byte & 0x80) {
            // Indexed Header Field (1xxxxxxx)
            printf("  Field #%d: Indexed Header Field (index=%d)\n", 
                   ++field_count, byte & 0x7F);
            pos++;
        } else if (byte & 0x40) {
            // Literal Header Field with Incremental Indexing (01xxxxxx)
            printf("  Field #%d: Literal with Incremental Indexing\n", ++field_count);
            pos++;
            // 跳过名称和值的长度字段（简化处理）
            if (pos < len && (data[pos] & 0x80)) {
                // Huffman encoded
                pos += 2; // 简化：假设长度为1字节
            } else {
                pos += 2; // 简化：假设长度为1字节
            }
        } else if (byte & 0x20) {
            // Dynamic Table Size Update (001xxxxx)
            printf("  Dynamic Table Size Update: %d\n", byte & 0x1F);
            pos++;
        } else {
            // Literal Header Field without Indexing (0000xxxx)
            printf("  Field #%d: Literal without Indexing\n", ++field_count);
            pos++;
            // 跳过字段内容（简化处理）
            if (pos < len) pos++;
        }
        
        // 防止无限循环
        if (pos >= len) break;
    }
    
    printf("  Total encoded size: %zu bytes\n", len);
    printf("  Fields processed: %d\n", field_count);
    printf("=== End Analysis ===\n\n");
}

int main() {
    printf("QPACK Wire Protocol Analysis Tool\n");
    printf("==================================\n\n");
    
    // 模拟第一个请求的 QPACK 编码数据（包含长头部）
    uint8_t request1_data[] = {
        // 这里应该是实际的 QPACK 编码数据
        // 由于我们没有实际的网络捕获，这里是示例数据
        0x00, 0x00, 0x51, 0x04, 0x2f,  // 示例：方法 GET
        0x50, 0x04, 0x68, 0x74, 0x74, 0x70,  // 示例：scheme http
        // ... 更多编码数据
    };
    
    // 模拟第二个请求的 QPACK 编码数据（应该使用索引）
    uint8_t request2_data[] = {
        // 第二个请求应该更短，因为使用了动态表索引
        0x80, 0x81, 0x82,  // 示例：使用索引的头部
        // ... 更少的数据
    };
    
    analyze_qpack_header_block(request1_data, sizeof(request1_data), 1);
    analyze_qpack_header_block(request2_data, sizeof(request2_data), 2);
    
    printf("Note: This is a demonstration tool.\n");
    printf("For real analysis, we need to capture actual QPACK encoded data\n");
    printf("from the network packets or HTTP/3 stream data.\n");
    
    return 0;
}
