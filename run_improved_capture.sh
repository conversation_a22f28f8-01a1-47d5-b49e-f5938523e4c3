#!/bin/bash

echo "=== 改进的 QPACK 抓包测试 ==="

# 设置环境变量
export SSLKEYLOGFILE=/home/<USER>/ngtcp2_qpack/quic_keylog_file

# 清理之前的文件
rm -f /home/<USER>/ngtcp2_qpack/qpack_test_improved.pcap
rm -f /home/<USER>/ngtcp2_qpack/quic_keylog_file

echo "1. 检测网络接口..."
echo "可用网络接口:"
ip link show | grep -E "^[0-9]+:" | awk '{print $2}' | sed 's/://'

# 获取默认网络接口
DEFAULT_INTERFACE=$(ip route | grep default | awk '{print $5}' | head -1)
echo "默认网络接口: $DEFAULT_INTERFACE"

echo ""
echo "2. 解析目标主机 IP..."
TARGET_IP=$(nslookup aliyun.hawks.top | grep -A1 "Name:" | grep "Address:" | awk '{print $2}' | head -1)
echo "目标 IP: $TARGET_IP"

echo ""
echo "3. 启动改进的抓包..."
# 使用更具体的过滤器和默认接口
sudo tcpdump -i $DEFAULT_INTERFACE -w /home/<USER>/ngtcp2_qpack/qpack_test_improved.pcap \
    -s 0 "host $TARGET_IP and port 443" &
TCPDUMP_PID=$!

echo "   抓包进程 PID: $TCPDUMP_PID"
echo "   抓包接口: $DEFAULT_INTERFACE"
echo "   过滤条件: host $TARGET_IP and port 443"

echo ""
echo "4. 等待抓包启动..."
sleep 3

echo "5. 运行 QPACK 测试..."
cd /home/<USER>/ngtcp2_qpack/ngtcp2/examples
./wsslclient -n 3 aliyun.hawks.top 443 http://aliyun.hawks.top/ \
    > qpack_improved_capture_test.log 2>&1

echo ""
echo "6. 等待数据传输完成..."
sleep 2

echo "7. 停止抓包..."
sudo kill $TCPDUMP_PID 2>/dev/null
sleep 2

echo ""
echo "8. 验证生成的文件..."
if [ -f /home/<USER>/ngtcp2_qpack/qpack_test_improved.pcap ]; then
    PCAP_SIZE=$(ls -lh /home/<USER>/ngtcp2_qpack/qpack_test_improved.pcap | awk '{print $5}')
    echo "   抓包文件大小: $PCAP_SIZE"
    
    # 使用 capinfos 检查数据包数量
    if command -v capinfos &> /dev/null; then
        PACKET_COUNT=$(capinfos /home/<USER>/ngtcp2_qpack/qpack_test_improved.pcap | grep "Number of packets" | awk '{print $4}')
        echo "   数据包数量: $PACKET_COUNT"
    fi
else
    echo "   ❌ 抓包文件未生成"
fi

if [ -f /home/<USER>/ngtcp2_qpack/quic_keylog_file ]; then
    KEYLOG_SIZE=$(ls -lh /home/<USER>/ngtcp2_qpack/quic_keylog_file | awk '{print $5}')
    KEYLOG_LINES=$(wc -l < /home/<USER>/ngtcp2_qpack/quic_keylog_file)
    echo "   密钥文件大小: $KEYLOG_SIZE"
    echo "   密钥文件行数: $KEYLOG_LINES"
    
    if [ $KEYLOG_LINES -gt 0 ]; then
        echo "   密钥文件前3行:"
        head -3 /home/<USER>/ngtcp2_qpack/quic_keylog_file | sed 's/^/     /'
    fi
else
    echo "   ❌ 密钥文件未生成"
fi

echo ""
echo "=== 改进的抓包测试完成 ==="
echo "抓包文件: /home/<USER>/ngtcp2_qpack/qpack_test_improved.pcap"
echo "密钥文件: /home/<USER>/ngtcp2_qpack/quic_keylog_file"
echo "测试日志: /home/<USER>/ngtcp2_qpack/ngtcp2/examples/qpack_improved_capture_test.log"
