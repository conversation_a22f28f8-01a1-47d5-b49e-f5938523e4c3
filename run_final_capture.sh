#!/bin/bash

echo "=== 最终 QPACK 抓包和解密测试 ==="

# 设置环境变量
export SSLKEYLOGFILE=/home/<USER>/ngtcp2_qpack/quic_keylog_file

# 清理之前的文件
rm -f /home/<USER>/ngtcp2_qpack/qpack_final.pcap
rm -f /home/<USER>/ngtcp2_qpack/quic_keylog_file

echo "1. 解析目标主机 IP..."
TARGET_IP=$(nslookup aliyun.hawks.top | grep -A1 "Name:" | grep "Address:" | awk '{print $2}' | head -1)
echo "   目标 IP: $TARGET_IP"

echo ""
echo "2. 启动抓包（使用正确的 IP 地址）..."
# 使用解析到的实际 IP 地址
sudo tcpdump -i eth0 -w /home/<USER>/ngtcp2_qpack/qpack_final.pcap \
    -s 0 "host $TARGET_IP and port 443" &
TCPDUMP_PID=$!

echo "   抓包进程 PID: $TCPDUMP_PID"
echo "   过滤条件: host $TARGET_IP and port 443"

echo ""
echo "3. 等待抓包启动..."
sleep 3

echo "4. 运行 QPACK 测试..."
cd /home/<USER>/ngtcp2_qpack/ngtcp2/examples

# 运行测试并显示实时输出
echo "   开始发送 HTTP/3 请求..."
timeout 30 ./wsslclient -n 3 aliyun.hawks.top 443 http://aliyun.hawks.top/ \
    > qpack_final_capture_test.log 2>&1

echo ""
echo "5. 等待数据传输完成..."
sleep 3

echo "6. 停止抓包..."
sudo kill $TCPDUMP_PID 2>/dev/null
sleep 2

echo ""
echo "7. 验证生成的文件..."
if [ -f /home/<USER>/ngtcp2_qpack/qpack_final.pcap ]; then
    PCAP_SIZE=$(ls -lh /home/<USER>/ngtcp2_qpack/qpack_final.pcap | awk '{print $5}')
    echo "   ✅ 抓包文件大小: $PCAP_SIZE"
    
    # 检查数据包数量
    if command -v capinfos &> /dev/null; then
        PACKET_COUNT=$(capinfos /home/<USER>/ngtcp2_qpack/qpack_final.pcap 2>/dev/null | grep "Number of packets" | awk '{print $4}')
        echo "   📦 数据包数量: $PACKET_COUNT"
    fi
else
    echo "   ❌ 抓包文件未生成"
fi

if [ -f /home/<USER>/ngtcp2_qpack/quic_keylog_file ]; then
    KEYLOG_SIZE=$(ls -lh /home/<USER>/ngtcp2_qpack/quic_keylog_file | awk '{print $5}')
    KEYLOG_LINES=$(wc -l < /home/<USER>/ngtcp2_qpack/quic_keylog_file)
    echo "   ✅ 密钥文件大小: $KEYLOG_SIZE"
    echo "   🔑 密钥文件行数: $KEYLOG_LINES"
    
    if [ $KEYLOG_LINES -gt 0 ]; then
        echo ""
        echo "   🔍 密钥文件内容预览:"
        head -3 /home/<USER>/ngtcp2_qpack/quic_keylog_file | sed 's/^/      /'
    fi
else
    echo "   ❌ 密钥文件未生成"
fi

echo ""
echo "8. 快速验证 QPACK 压缩效果..."
if [ -f qpack_final_capture_test.log ]; then
    echo "   📊 QPACK 压缩数据:"
    grep "Total encoded size" qpack_final_capture_test.log | head -3 | sed 's/^/      /'
    
    echo ""
    echo "   🎯 长头部配置:"
    grep "Long value header" qpack_final_capture_test.log | head -3 | sed 's/^/      /'
fi

echo ""
echo "=== 最终抓包测试完成 ==="
echo "📁 生成的文件:"
echo "   抓包文件: /home/<USER>/ngtcp2_qpack/qpack_final.pcap"
echo "   密钥文件: /home/<USER>/ngtcp2_qpack/quic_keylog_file"
echo "   测试日志: /home/<USER>/ngtcp2_qpack/ngtcp2/examples/qpack_final_capture_test.log"

echo ""
echo "🔍 下一步分析命令:"
echo "   # 使用 tshark 解密分析:"
echo "   tshark -r /home/<USER>/ngtcp2_qpack/qpack_final.pcap \\"
echo "     -o tls.keylog_file:/home/<USER>/ngtcp2_qpack/quic_keylog_file \\"
echo "     -Y \"quic\" -c 10"
echo ""
echo "   # 分析 HTTP/3 流量:"
echo "   tshark -r /home/<USER>/ngtcp2_qpack/qpack_final.pcap \\"
echo "     -o tls.keylog_file:/home/<USER>/ngtcp2_qpack/quic_keylog_file \\"
echo "     -Y \"http3\" -V"
