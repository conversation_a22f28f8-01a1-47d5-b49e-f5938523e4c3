#!/bin/bash

PCAP_FILE="/home/<USER>/ngtcp2_qpack/qpack_test.pcap"
KEYLOG_FILE="/home/<USER>/ngtcp2_qpack/quic_keylog_file"
OUTPUT_DIR="/home/<USER>/ngtcp2_qpack/analysis"

mkdir -p $OUTPUT_DIR

echo "=== QPACK 抓包分析开始 ==="

# 检查文件是否存在
if [ ! -f "$PCAP_FILE" ]; then
    echo "错误: 抓包文件不存在: $PCAP_FILE"
    exit 1
fi

if [ ! -f "$KEYLOG_FILE" ]; then
    echo "错误: 密钥文件不存在: $KEYLOG_FILE"
    exit 1
fi

echo "抓包文件: $PCAP_FILE"
echo "密钥文件: $KEYLOG_FILE"
echo "输出目录: $OUTPUT_DIR"

# 检查 tshark 是否可用
if ! command -v tshark &> /dev/null; then
    echo "安装 tshark..."
    sudo apt update && sudo apt install -y tshark
fi

echo "" 
echo "=== 生成分析报告 ==="

# 创建分析报告
cat > $OUTPUT_DIR/qpack_analysis_report.txt << EOF
=== QPACK 抓包分析报告 ===
生成时间: $(date)
抓包文件: $PCAP_FILE
密钥文件: $KEYLOG_FILE

EOF

echo "1. 分析抓包文件基本信息..."
echo "1. 抓包文件基本信息:" >> $OUTPUT_DIR/qpack_analysis_report.txt
capinfos $PCAP_FILE >> $OUTPUT_DIR/qpack_analysis_report.txt 2>/dev/null || echo "无法获取抓包文件信息" >> $OUTPUT_DIR/qpack_analysis_report.txt

echo "" >> $OUTPUT_DIR/qpack_analysis_report.txt

echo "2. 分析 QUIC 连接..."
echo "2. QUIC 连接信息:" >> $OUTPUT_DIR/qpack_analysis_report.txt
tshark -r $PCAP_FILE -o tls.keylog_file:$KEYLOG_FILE \
    -Y "quic" -T fields -e frame.number -e quic.packet_type -e quic.scid -e quic.dcid \
    >> $OUTPUT_DIR/qpack_analysis_report.txt 2>/dev/null || echo "无法解析 QUIC 数据" >> $OUTPUT_DIR/qpack_analysis_report.txt

echo "" >> $OUTPUT_DIR/qpack_analysis_report.txt

echo "3. 分析 HTTP/3 流量..."
echo "3. HTTP/3 流量信息:" >> $OUTPUT_DIR/qpack_analysis_report.txt
tshark -r $PCAP_FILE -o tls.keylog_file:$KEYLOG_FILE \
    -Y "http3" -T fields -e frame.number -e http3.frame_type -e http3.stream_id \
    >> $OUTPUT_DIR/qpack_analysis_report.txt 2>/dev/null || echo "无法解析 HTTP/3 数据" >> $OUTPUT_DIR/qpack_analysis_report.txt

echo "" >> $OUTPUT_DIR/qpack_analysis_report.txt

echo "4. 查找 QPACK 相关数据..."
echo "4. QPACK 编码器/解码器流:" >> $OUTPUT_DIR/qpack_analysis_report.txt
tshark -r $PCAP_FILE -o tls.keylog_file:$KEYLOG_FILE \
    -Y "http3.stream_type == 2 or http3.stream_type == 3" -V \
    >> $OUTPUT_DIR/qpack_analysis_report.txt 2>/dev/null || echo "无法找到 QPACK 流数据" >> $OUTPUT_DIR/qpack_analysis_report.txt

echo "" >> $OUTPUT_DIR/qpack_analysis_report.txt

echo "5. 分析 HTTP/3 设置..."
echo "5. HTTP/3 SETTINGS 帧:" >> $OUTPUT_DIR/qpack_analysis_report.txt
tshark -r $PCAP_FILE -o tls.keylog_file:$KEYLOG_FILE \
    -Y "http3.frame_type == 4" -V \
    >> $OUTPUT_DIR/qpack_analysis_report.txt 2>/dev/null || echo "无法找到 SETTINGS 帧" >> $OUTPUT_DIR/qpack_analysis_report.txt

echo "分析完成！"
echo ""
echo "=== 生成的文件 ==="
ls -la $OUTPUT_DIR/

echo ""
echo "=== 快速预览分析结果 ==="
echo "报告文件大小: $(ls -lh $OUTPUT_DIR/qpack_analysis_report.txt | awk '{print $5}')"
echo ""
echo "报告前20行预览:"
head -20 $OUTPUT_DIR/qpack_analysis_report.txt

echo ""
echo "=== 分析完成 ==="
echo "完整报告位置: $OUTPUT_DIR/qpack_analysis_report.txt"
